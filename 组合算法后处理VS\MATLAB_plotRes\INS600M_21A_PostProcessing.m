%*************20240815*****post test******
clc;
close all;
DEG2RAD=pi/180;
ResData=inavresult3;     %算法结果
ObsData=inavdata3;       %观测数据
StandData=inavstandard3; %标定数据
ResData(1,:)=[];    ObsData(1,:)=[];    StandData(1,:)=[]; 

GPSdat=[];
Len=length(ObsData(:,1));
GPStow=ObsData(:,21);
fstper=ObsData(1,:);
GPSdat=[GPSdat;fstper];
for k=2:Len
    per=ObsData(k,:);
    if(GPStow(k)-GPStow(k-1)>10)
       GPSdat=[GPSdat;per];
    end
end

%*********level frame********
%*******轨迹****
figure;hold on;
plot(ResData(:,3),ResData(:,2),'.-r');xlabel('lon/degree');ylabel('lat/degree');
plot(GPSdat(:,9),GPSdat(:,8),'.-b'); title('red->algrithm trace, blue->GNSS trace');
%***高度*****
figure;hold on;
plot(ResData(:,1).*0.001,ResData(:,4),'.-r');
plot(GPSdat(:,20).*0.001,GPSdat(:,10),'.-b'); title('red->algrithm alt, blue->GNSS alt');
%*****速度*******
figure;
subplot(3,1,1);hold on; plot(ResData(:,1)*0.001,ResData(:,5),'.-r');plot(GPSdat(:,20)*0.001,GPSdat(:,11),'.-b');title('Ve');
subplot(3,1,2);hold on; plot(ResData(:,1)*0.001,ResData(:,6),'.-r');plot(GPSdat(:,20)*0.001,GPSdat(:,12),'.-b');title('Vn');
subplot(3,1,3);hold on; plot(ResData(:,1)*0.001,ResData(:,7),'.-r');plot(GPSdat(:,20)*0.001,GPSdat(:,13),'.-b');title('Vu');
%*****姿态*******
figure;
subplot(3,1,1); plot(ResData(:,1)*0.001,ResData(:,8),'.-r');title('pitch');
subplot(3,1,2); plot(ResData(:,1)*0.001,ResData(:,9),'.-g');title('roll');
%subplot(3,1,3);hold on; plot(ResData(:,1)*0.001,ResData(:,10),'.-r');plot(GPSdat(:,20)*0.001,GPSdat(:,14),'.-b');title('yaw');
figure;hold on;
plot(ResData(:,1)*0.001,ResData(:,10),'.-r');plot(GPSdat(:,20)*0.001,GPSdat(:,14),'.-b');title('yaw');
figure;plot(ResData(:,1)*0.001,ResData(:,13),'.-r');title('RTK Status');
figure;plot(ResData(:,1)*0.001,ResData(:,14),'.-g');title('PPSdelay /s');
figure;plot(ResData(:,1)*0.001,ResData(:,15),'.-b');title('PDOP');
%**********标定信息************
figure;hold on;%*****gyro bias**deg/s**
 plot(StandData(:,37)*0.001,StandData(:,1),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,2),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,3),'.-b');title('Bias rgb->xyz /deg/s');
figure;hold on;%*****acc bias**mg**
 plot(StandData(:,37)*0.001,StandData(:,4),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,5),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,6),'.-b');title('Bias rgb->xyz /mg');
%**********P std****15维************
figure;hold on;%phi
 plot(StandData(:,37)*0.001,StandData(:,7),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,8),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,9),'.-b');title('rgb->phi ENU /  deg/s');
% figure;hold on;%E vel
%  plot(StandData(:,37)*0.001,StandData(:,10),'.-r');%,
%  plot(StandData(:,37)*0.001,StandData(:,11),'.-g'); 
%  plot(StandData(:,37)*0.001,StandData(:,12),'.-b');title('rgb->P vel /  m/s');
% figure;hold on;%E pos
%  plot(StandData(:,37)*0.001,StandData(:,13),'.-r');%,
%  plot(StandData(:,37)*0.001,StandData(:,14),'.-g'); title('rg->P ll /  deg');
% figure;
%  plot(StandData(:,37)*0.001,StandData(:,15),'.-b');title('P height /  m');
figure;hold on;%P eb
 plot(StandData(:,37)*0.001,StandData(:,16),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,17),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,18),'.-b');title('rgb->E ebxyz /  deg/s');
figure;hold on;%P db
 plot(StandData(:,37)*0.001,StandData(:,19),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,20),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,21),'.-b');title('rgb->E dbxyz /  mg');
%*****************b2ODS******************
figure;hold on;%att_ods2_b_filte
 plot(StandData(:,37)*0.001,StandData(:,22),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,23),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,24),'.-b');title('rgb->att-ods2b-filtexyz /  deg');
figure;hold on;%att_ods2_b_filte->PK_std
 plot(StandData(:,37)*0.001,StandData(:,25),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,26),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,27),'.-b');title('rgb->att-ods2b-filtexyz->PK-std /  deg');
%***********b2GNSS**********
figure;hold on;%att_b2gps
 plot(StandData(:,37)*0.001,StandData(:,28),'.-r');%,
 plot(StandData(:,37)*0.001,StandData(:,29),'.-g'); 
 plot(StandData(:,37)*0.001,StandData(:,30),'.-b');title('rgb->att-b2gps /  deg');
figure;plot(StandData(:,37)*0.001,StandData(:,31),'.-k');title('Pk-std of att-b2gps[2] /deg');
%*******odo factor filter********
figure;plot(StandData(:,37)*0.001,StandData(:,32),'.-r');title('odo-factor-filte ');
figure;plot(StandData(:,37)*0.001,StandData(:,33),'.-g');title('PK-std odo-factor-filte ');
%***********odo 测量信息*********
figure;
subplot(2,1,1); plot(StandData(:,37)*0.001,StandData(:,34),'.-r');title('odo vel/m/s');
subplot(2,1,2); plot(StandData(:,37)*0.001,StandData(:,36),'.-b');title('odo gear');
%**********IMU data**************
figure;
subplot(3,1,1); plot(inavdata3(:,20)*0.001,inavdata3(:,1)./DEG2RAD,'.-r');title('wx/deg/s');
subplot(3,1,2); plot(inavdata3(:,20)*0.001,inavdata3(:,2)./DEG2RAD,'.-g');title('wy/deg/s');
subplot(3,1,3); plot(inavdata3(:,20)*0.001,inavdata3(:,3)./DEG2RAD,'.-b');title('wz/deg/s');
figure;
subplot(3,1,1); plot(inavdata3(:,20)*0.001,inavdata3(:,4),'.-r');title('ax/m/s2');
subplot(3,1,2); plot(inavdata3(:,20)*0.001,inavdata3(:,5),'.-g');title('ay/m/s2');
subplot(3,1,3); plot(inavdata3(:,20)*0.001,inavdata3(:,6),'.-b');title('az/m/s2');

% figure;plot(inavstandard3(:,37)*0.001,inavstandard3(:,38),'.-b');title('fog_kz');
% figure;plot(inavstandard3(:,37)*0.001,inavstandard3(:,39),'.-k');title('Pk-fog/sqrt');
%*************比较轮速跟GPS速度的差异**************
GPSVEL=(GPSdat(:,11).^2+GPSdat(:,12).^2+GPSdat(:,13).^2).^0.5;
myGPSvel=(ObsData(:,11).^2+ObsData(:,12).^2+ObsData(:,13).^2).^0.5;
figure;plot(abs(abs(StandData(:,34))-myGPSvel),'.-k');title('ODO与GPS速度差值');


