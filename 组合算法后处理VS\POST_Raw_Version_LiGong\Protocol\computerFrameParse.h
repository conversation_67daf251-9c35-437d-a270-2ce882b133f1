#ifndef ____COMPUTER_FRAME_PARSE_H____
#define ____COMPUTER_FRAME_PARSE_H____
#include <stdint.h>
#include "algorithm.h"

typedef  struct
{
    float	S_ax;
    float	M_axy;
    float	M_axz;
		float	B_ax;
	
    float	S_ay;
    float	M_ayx;
    float	M_ayz;
		float	B_ay;
	
    float	S_az;
    float	M_azx;
    float	M_azy;  
    float	B_az;
} __8k21_cali_t;


typedef  struct calib_t
{
    unsigned short 		imuSelect;
    __8k21_cali_t 	accelCali;
    __8k21_cali_t 	gyroCali;
    AdjPara_t		adj;
    uint32_t 		hash;
} CalibTypeDef;

extern void comm_saveCaliData(void);
extern void wheel_is_running(void);

#endif // ____COMPUTER_FRAME_PARSE_H____


