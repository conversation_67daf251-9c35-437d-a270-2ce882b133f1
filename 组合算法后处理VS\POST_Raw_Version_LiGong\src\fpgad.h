#ifndef __FPGAD_H
#define __FPGAD_H

typedef struct _fpgadata 
{
	unsigned short	DATA_LEN;
	unsigned short	NUM_CLK;
	short	Dtemp_data;
	short	Utemp_data;
	short	accZ_data;
	short	accY_data;
	short	accX_data;
	short	rateZ_data;
	short	rateY_data;
	short	rateX_data;
	unsigned short	hGPSData_gpsweek;
	unsigned short	hGPSData_gpssecond0;
	unsigned short	hGPSData_gpssecond1;
	unsigned short	hGPSData_headingStatus;
	unsigned short	hGPSData_rtkStatus;	
	unsigned short	hGPSData_vn0;
	unsigned short	hGPSData_vn1;
	unsigned short	hGPSData_ve0;
	unsigned short	hGPSData_ve1;
	unsigned short	hGPSData_vu0;
	unsigned short	hGPSData_vu1;
	unsigned short	hGPSData_Heading0;
	unsigned short	hGPSData_Heading1;
	unsigned short	hGPSData_Pitch0;
	unsigned short	hGPSData_Pitch1;
	unsigned short	hGPSData_Roll0;
	unsigned short	hGPSData_Roll1;
	unsigned short	hGPSData_Lat0;
	unsigned short	hGPSData_Lat1;
	unsigned short	hGPSData_Lat2;
	unsigned short	hGPSData_Lat3;
	unsigned short	hGPSData_Lon0;
	unsigned short	hGPSData_Lon1;
	unsigned short	hGPSData_Lon2;
	unsigned short	hGPSData_Lon3;
	unsigned short	hGPSData_Alt0;
	unsigned short	hGPSData_Alt1;
	unsigned short	hGPSData_Alt2;
	unsigned short	hGPSData_Alt3;
	unsigned short	GPGGA_STAR;
	unsigned short	HEADING_BAS0;
	unsigned short	HEADING_BAS1;
	unsigned short	GPRMC_POS;
	unsigned short	GPRMC_LON;
	unsigned short	GPRMC_LAT;
	unsigned short	GPRMC_TRA[3];
//	unsigned short	Heading0;
//	unsigned short	Heading1;
//	unsigned short	Pitch0;
//	unsigned short	Pitch1;
	unsigned short	gpssecond9820;
	unsigned short	gpssecond9821;
	unsigned short	sensorTemp;
	unsigned short	gyroGrp20;
	unsigned short	gyroGrp21;
	unsigned short	velStatus;
	
	unsigned short	VERSION;   
} fpgadata_t;

extern	fpgadata_t	gpagedata;

#endif /* __FPGAD_H */
