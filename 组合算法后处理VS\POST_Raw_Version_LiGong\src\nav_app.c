/***********************************************************************************
nav application module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|DengWei       |  2023-2-13         | Modify                     |
 Add EPSON_G370 sensor
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
#include <string.h>
#include <stdio.h>
#include "gnss.h"
//#ifndef linux
//#include "UartAdapter.h"
//#endif
//int timecount = 0;
//全局变量 
//NAV_Data_t NAV_Data;  

//Sensors_Data_t Sensors_Data = {0};
//Param_Data_t Param_Data = {0};
//Param_t Param_Data_temp = {0};
//CombineDataTypeDef *Sensors = &combineData; //传感器数据指针
char g_ALGODebugInfo[1024]={0};
unsigned long int g_NavIndex=0;

#if 0
//陀螺观测量滤波
double P_gyro_Cvn=(100.0)*(100.0);
double Q_gyro_Cvn =(0.001f)*(0.001f)*0.005;
double R_gyro_Cvn =(1.0f)*(1.0f);

//加计观测量滤波
double P_acc_Cvn=(20.0)*(20.0);
double Q_acc_Cvn =(0.001f)*(0.001f)*0.005;
double R_acc_Cvn =(1.0f)*(1.0f);
#endif

///////////////////////////////////////////////////////////////////////////////////

//导航结构体变量定义
_NAV_Data_Full_t NAV_Data_Full = {0};	 //导航总数据
_NAV_Data_Out_t NAV_Data_Out = {0};      //输出数据
_NAV_Funsion_Status_Time_t g_NAV_Funsion_Status_Time={0};//融合状态及持续时间




//矫正航向角范围到(-180 180]
double CorrHeading(double orginheading)
{
	if(orginheading<=-180)
	{
		return orginheading+360;
	}
	else if(orginheading>180)
	{
		return orginheading-360;
	}
	else
	{
		return orginheading;
	}
}
//****Guolong Zhang*****[-pi~pi]*****
double CorrHeading_PI(double orginheading)//*****输入为rad***
{
	if(orginheading<=-PI)
	{
		return orginheading+2*PI;
	}
	else if(orginheading>PI)
	{
		return orginheading-2*PI;
	}
	else
	{
		return orginheading;
	}
}



/******************************************************************************
*原  型：void KinematicEstimation(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：运动学模型估计
*输  入：无
*输  出：无
*******************************************************************************/
void KinematicEstimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#if 0
	//估计载体状态
	//包括1:静止
	//静止条件:上个时刻速度接近0， 导航系下没有加速度计角速度
	if(		NAV_Data_Full_p->SINS.vn[0]<MAX_STATIC_VN 
		&& 	NAV_Data_Full_p->SINS.vn[1]<MAX_STATIC_VN 
		&&   )
#endif		
	
}

/******************************************************************************
*原  型：void Param_Data_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：参数初始化
*输  入：无
*输  出：无
*******************************************************************************/
void Param_Data_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#ifndef WIN32
	combineData.Param.methodType =	E_METHOD_TYPE_KALMAN;
	combineData.Param.sim = E_SIM_MODE_NORMAL;
#endif
	//NAV_Data_Full_p->KF.use_gps_flag 		=	E_FUNSION_GPS;	
	NAV_Data_Full_p->KF.use_gps_flag = E_FUNSION_NONE;
	NAV_Data_Full_p->Gnss_Use_Status = E_NAV_SUPPORT_RTK_ALL_STATUS;//E_NAV_SUPPORT_RTK_FIEX_STATUS;//E_NAV_SUPPORT_RTK_ALL_STATUS;//E_NAV_SUPPORT_RTK_FIEX_STATUS
		
	NAV_Data_Full_p->ODS.ods_flag 	= E_ODS_WHEEL_FLAG_NONE;//;E_ODS_WHEEL_FLAG_NONE;E_ODS_WHEEL_FLAG_HAVE
	NAV_Data_Full_p->ODS.ods_caculat_flag = RETURN_FAIL;//******

	//if(E_IMU_MANU_460 == NAV_Data_Full_p->memsType)
	//{
	//	NAV_Data_Full_p->KF.measure_flag_head	=	E_KALMAN_MEASURE_HEADING_YES;//E_KALMAN_MEASURE_HEADING_NO
	//}
	//else if(E_IMU_MANU_ADIS16465 == NAV_Data_Full_p->memsType)
	//{
	//	NAV_Data_Full_p->KF.measure_flag_head =	E_KALMAN_MEASURE_HEADING_YES;
	//}
	//else
	//{
	//	NAV_Data_Full_p->KF.measure_flag_head	= E_KALMAN_MEASURE_HEADING_NO;
	//}
	
	NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;
	NAV_Data_Full_p->KF.measure_flag_Wheel = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_ZUPT = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_vn = E_KALMAN_MEASURE_VEL_NO;
	NAV_Data_Full_p->KF.measure_flag_pos = E_KALMAN_MEASURE_POS_NO;
	NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_NONE;
}

void NavDRMode(unsigned int testcount)
{

	if(	   (NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
		&& NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
		&& E_SIM_MODE_NORMAL == combineData.Param.sim
	   )
	{
		
//		SINS_UP(&NAV_Data_Full);
//		KF_UP2(&NAV_Data_Full,&combineData);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
	else if(		(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
				&& NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
				&& E_SIM_MODE_DEBUG == combineData.Param.sim
				&& testcount<combineData.Param.lostepoch
	   		)
	{
		//SINS_UP(&NAV_Data_Full);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
	else
	{
//		SetNavFunsionSource(E_FUNSION_WHEEL);
//		SINS_UP_DR(&NAV_Data_Full);
//		//直接使用轮速进行速度更新
//		TEST_UP(&NAV_Data_Full);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
}

void NavKalmanMode(unsigned int testcount)
{
	#if 0
	if(0 == combineData.Param.HP)
	{
		SINS_UP(&NAV_Data_Full);
	}
	else
	{
		//SINS_UP_HP(&NAV_Data_Full);
		SINS_UP(&NAV_Data_Full);
	}
	#endif
	SINS_Update(&NAV_Data_Full);
	// ========== 船载应用：升沉补偿处理 ==========
// TODO: 需要将 nav_heave.c 添加到编译项目中才能启用此功能
	 //HeaveCompensation_Process(&NAV_Data_Full);  // 升沉补偿算法
	//if(E_SIM_MODE_DEBUG == combineData.Param.sim)
	//{
	//	if(testcount>=combineData.Param.lostepoch)
	//	{
	//		//NAV_Data_Full.KF.use_gps_flag = E_FUNSION_WHEEL;//E_FUNSION_WHEEL;//E_FUNSION_MOTION;
	//		//hGPSData.rtkStatus = 0 ;
	//		NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
	//	}
	//}
	KF_UP2(&NAV_Data_Full,&combineData);

	// DVL calibration and estimation
	DVL_Estimation(&NAV_Data_Full);

	// ODS calibration and estimation
	ODS_Angle_Estimation(&NAV_Data_Full);
}

//函数定义
/******************************************************************************
*原  型：void NAV_function(void)
*功  能：导航解算主函数，用于车载六轴+ODS+GPS
*输  入：无
*输  出：无
*******************************************************************************/
void NAV_function(void)//导航解算函数
{
	static unsigned long row=0;
	row++;
	if(row%10000 ==0)
	printf("解算跑车数据第%ld行\n",row);

	//static long int navindex=0;
	static unsigned long testcount=0;
	g_NavIndex++;

	//获取传感器数据 和 系统参数
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	Get_ODS_Data(&NAV_Data_Full,&combineData);
	Get_DVL_Data(&NAV_Data_Full, &combineData);

    //运动学估计Kinematic estimation
	//KinematicEstimation(&NAV_Data_Full);
	//*******仿真GPS失锁****初始化10min后开始执行*****
	//int i = 0;
	//if(NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)
	//for (i = 0; i < outnum; i++)
	//{
	//	if ((NAV_Data_Full.GPS.gpssecond982 / 1000.0 - NAV_Data_Full.outage_start_per[i] > 0.0)
	//		&& (NAV_Data_Full.GPS.gpssecond982 / 1000.0 - NAV_Data_Full.outage_start_per[i] < outagetime))
	//	{
	//		//NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
	//		//NAV_Data_Full.GPS.rtkStatus = E_GPS_RTK_INVALID;
	//		//hGPSData.rtkStatus = E_GPS_RTK_INVALID;//*******用于上位机显示0****
	//		//NAV_Data_Full.GPS.Position_Status = E_GPS_POS_INVALID;
	//	}
	//}
	//testcount++;
	switch(NAV_Data_Full.Nav_Status)
	{
	    case E_NAV_STATUS_START://开始导航 加载导航参数0
		{
			//加载配置参数
			Param_Data_Init(&NAV_Data_Full);			
			//加载标定参数,标定参数在观测量中读取，不在这加载
			Load_Standard_Data(&NAV_Data_Full,&combineData);//**************
			SetNavStatus(E_NAV_STATUS_ROUTH_ALIGN); 
		    //NAV_Data_Full.Nav_Status = E_NAV_STATUS_ROUTH_ALIGN;  
		}
		break;

		case E_NAV_STATUS_ROUTH_ALIGN://SINS初始对准1
		{
			if(	//(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) &&
			     (NAV_Data_Full.GPS.Position_Status != E_GPS_POS_INVALID) &&
			     (NAV_Data_Full.GPS.gps_up_flag == E_GPS_IS_UPDATE) &&
			     (NAV_Data_Full.GPS.headingStatus	== E_GPS_RTK_FIXED)		
			  )						
			{	
				NAV_Data_Full.GPSlastnum++;
				if (NAV_Data_Full.GPSlastnum > 25)//******5Hz**持续5s***
				{
					// GPS信号稳定持续5秒以上，可以进入KF初始化阶段
					SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
				}
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_SINS_KF_INITIAL;// NAV_INS_STATUS_ROUTH_ALIGN;
			}			
		}
		break;

		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF初始化2
		{
 			SINS_Init(&NAV_Data_Full);			
			if(NAV_Data_Full.SINS.Init_flag == 1)
			{
				if(NAV_Data_Full.Nav_Standard_flag ==E_NAV_STANDARD_PROCCSSED)
				{
					SetNavStatus(E_NAV_STATUS_IN_NAV);
					//g_KF_UP2_gps_delay_cunt = 0;//初始切换到正在导航需要设置为0
					//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
				}
				else
				{
					SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
					//g_KF_UP2_gps_delay_cunt = 0;//初始切换到系统标定需要设置为0
					//NAV_Data_Full.Nav_Status = E_NAV_STATUS_SYSTEM_STANDARD;
				}
				KF_Init(&NAV_Data_Full); 
			}

		}
		break;
		case E_NAV_STATUS_SYSTEM_STANDARD://系统标定*****3
		{
			SINS_Update(&NAV_Data_Full);
			KF_UP2(&NAV_Data_Full,&combineData);

			// DVL calibration and estimation
			//DVL_Estimation(&NAV_Data_Full);

			// ODS calibration and estimation
			//ODS_Angle_Estimation(&NAV_Data_Full);

			if(NAV_Data_Full.Nav_Standard_flag ==E_NAV_STANDARD_PROCCSSED)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
				//KF_Init(&NAV_Data_Full);
				//g_KF_UP2_gps_delay_cunt = 0;//初始切换到正在导航需要设置为0
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
				//保存标定数据
				//Save_Standard_Data(&NAV_Data_Full,&combineData);
#ifdef WIN32
				inav_log(INAVMD(LOG_DEBUG),"gnssAtt_from_vehicle2=%f",NAV_Data_Full.Param.gnssAtt_from_vehicle2[2]);
#endif
			}
		}
		break;
		case E_NAV_STATUS_IN_NAV://正常导航 *****4
		{	
			++testcount;		
#if 1
			if(E_METHOD_TYPE_KALMAN == combineData.Param.methodType)
			{
				NavKalmanMode(g_NavIndex);//
			}
		
//			}
#endif
		}
		break;

		case E_NAV_STATUS_STOP://停止导航
		{
 			//StopNavigation();
		}
		break;
		
		default: //其它异常状态
		{
			break;	
		}
	}

	#ifdef WIN32	
	//打印当前历元数目、状态迁移Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	//if(NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)
	Out_Data_Up(&NAV_Data_Out); //输出数据更新到NAV_Data_Out

}

//函数定义
/******************************************************************************
*原  型：void NAV_function_LD_TEST(void)
*功  能：导航解算主函数，
*输  入：无
*输  出：无
*******************************************************************************/
void NAV_function_LD_TEST(void)//导航解算函数
{
	//获取传感器数据和系统参数
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	NAV_Data_Full.GPS.Heading = 0;
	Get_ODS_Data(&NAV_Data_Full,&combineData);
	
	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://开始导航 加载导航参数
		{
			//加载配置参数
			Param_Data_Init(&NAV_Data_Full);
			//加载标定参数,标定参数在观测量中读取，不在这加载
			Load_Standard_Data(&NAV_Data_Full,&combineData);
			SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
		}
		break;	
		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF初始化
		{
			NAV_Data_Full.SINS.pos[0]	=	22.74418555666*DEG2RAD;//pNAV_GNSS_RESULT->latitude*DEG2RAD;//
			NAV_Data_Full.SINS.pos[1]	=	113.8147628866*DEG2RAD;//pNAV_GNSS_RESULT->longitude * DEG2RAD;//
			NAV_Data_Full.SINS.pos[2]	=	0;//pNAV_GNSS_RESULT->altitude ;//高程单位m 
			NAV_Data_Full.SINS.vn[0] = 0;
			NAV_Data_Full.SINS.vn[1] = 0;
			NAV_Data_Full.SINS.vn[2] = 0;
 			SINS_Init(&NAV_Data_Full);			
			SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
			
		}
		break;
		case E_NAV_STATUS_SYSTEM_STANDARD://系统标定
		{
				Earth_UP(&NAV_Data_Full,NAV_Data_Full.SINS.pos,NAV_Data_Full.SINS.vn);
				MahonyUpdate_NoMAG(&NAV_Data_Full);	
		}
		break;
	}
#ifdef WIN32	
	//打印当前历元数目、状态迁移Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	Out_Data_Up(&NAV_Data_Out); //输出数据更新

}

//函数定义
/******************************************************************************
*原  型：NAV_function_boad_sins_TEST(void)
*功  能：纯惯性+升沉，
*输  入：无
*输  出：无
*******************************************************************************/
void NAV_function_boad_sins_TEST(void)//导航解算函数
{
	static unsigned long row = 0;
	row++;
	if (row % 10000 == 0)
		printf("解算数据第%ld行\n", row);
	g_NavIndex++;
	//获取传感器数据 和 系统参数
	Get_Param_Data(&NAV_Data_Full, &combineData);
	Get_IMU_Data(&NAV_Data_Full, &combineData); 

	switch (NAV_Data_Full.Nav_Status)
	{
	case E_NAV_STATUS_START://开始导航 加载导航参数
	{
		//加载配置参数
		Param_Data_Init(&NAV_Data_Full);
		//加载标定参数,标定参数在观测量中读取，不在这加载
		Load_Standard_Data(&NAV_Data_Full, &combineData);
		SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
	}
	break;
	case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF初始化
	{
		//NAV_Data_Full.SINS.pos[0] = 22.74418555666 * DEG2RAD;//pNAV_GNSS_RESULT->latitude*DEG2RAD;//
		//NAV_Data_Full.SINS.pos[1] = 113.8147628866 * DEG2RAD;//pNAV_GNSS_RESULT->longitude * DEG2RAD;//
		//NAV_Data_Full.SINS.pos[2] = 0;//pNAV_GNSS_RESULT->altitude ;//高程单位m 
		//NAV_Data_Full.SINS.vn[0] = 0;
		//NAV_Data_Full.SINS.vn[1] = 0;
		//NAV_Data_Full.SINS.vn[2] = 0;
		SINS_Init(&NAV_Data_Full);
		if (NAV_Data_Full.SINS.Init_flag == 1) {
			SetNavStatus(E_NAV_STATUS_IN_NAV);
		}
		

	}
	break;
	case E_NAV_STATUS_IN_NAV:
	{
		SINS_Update(&NAV_Data_Full);
		HeaveCompensation_Process(&NAV_Data_Full);  // 升沉补偿算法
	}
	break;
	}

#ifdef WIN32	
	//打印当前历元数目、状态迁移Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex, &NAV_Data_Full);
	//NMEA out
#endif
	Out_Data_Up(&NAV_Data_Out); //输出数据更新

}


//函数定义
/******************************************************************************
*原  型：void NAV_function_UAV(void)
*功  能：导航解算主函数，用于九轴无人机
*输  入：无
*输  出：无
*******************************************************************************/
void NAV_function_UAV(void)//导航解算函数
{
	g_NavIndex++;
	//获取传感器数据 和 系统参数
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);

	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://开始导航 加载导航参数
		{
			//加载配置参数
			Param_Data_Init(&NAV_Data_Full);
			//加载标定参数,标定参数在观测量中读取，不在这加载
			Load_Standard_Data(&NAV_Data_Full,&combineData);
			SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
		    //NAV_Data_Full.Nav_Status = E_NAV_STATUS_ROUTH_ALIGN;  
		}
		break;

		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF初始化
		{
 			SINS_Init(&NAV_Data_Full);			
			if(NAV_Data_Full.SINS.Init_flag == 1)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
			}
		}
		break;

		case E_NAV_STATUS_IN_NAV://正常导航 
		{
			MahonyUpdate(&NAV_Data_Full);	
		}
		break;
		case E_NAV_STATUS_STOP://停止导航
		{
 			//StopNavigation();
		}
		break;
		default: //其它异常状态
		{
			break;	
		}
	}
#ifdef WIN32	
	//打印当前历元数目、状态迁移Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	//输出ASCII协议数据
	Out_Data_Up(&NAV_Data_Out); //输出数据更新
}


void NAV_function_presins(void)//纯惯性导航函数
{
	// ========== 数据获取阶段 ==========
	// 只获取IMU数据（加表和陀螺数据），不获取GPS和里程计数据
	static unsigned long row = 0;
	row++;
	if (row % 10000 == 0)
		printf("解算数据第%ld行\n", row);
	g_NavIndex++;
	Get_Param_Data(&NAV_Data_Full, &combineData);  // 获取导航参数
	Get_IMU_Data(&NAV_Data_Full, &combineData);    // 获取IMU数据（加速度计和陀螺仪）

	// ========== 纯惯性导航状态机 ==========
	switch (NAV_Data_Full.Nav_Status)
	{
	case E_NAV_STATUS_START://开始状态 加载标定参数
	{
		// 导航参数初始化设置
		Param_Data_Init(&NAV_Data_Full);
		// 加载标定参数，标定参数从观测量中读取或直接设置
		Load_Standard_Data(&NAV_Data_Full, &combineData);
		// 直接跳转到SINS和KF初始化状态
		SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
	}
	break;
	case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF初始化
	{
		 //========== 设定初始位置（经纬度和高度）==========
		 //设定初始位置：深圳坐标（可根据需要修改）
		//NAV_Data_Full.SINS.pos[0] = 22.74418555666 * DEG2RAD;  // 初始纬度（弧度）
		//NAV_Data_Full.SINS.pos[1] = 113.8147628866 * DEG2RAD;  // 初始经度（弧度）
		//NAV_Data_Full.SINS.pos[2] = 0;                       // 初始高度（米）

		//// ========== 设定初始速度 ==========
		//// 初始速度设为0（静止状态启动）
		//NAV_Data_Full.SINS.vn[0] = 0.0;  // 北向速度（m/s）
		//NAV_Data_Full.SINS.vn[1] = 0.0;  // 东向速度（m/s）
		//NAV_Data_Full.SINS.vn[2] = 0.0;  // 天向速度（m/s）

		//// ========== 设定初始姿态 ==========
		//// 初始姿态设为水平状态，航向角为0（正北方向）
		////NAV_Data_Full.SINS.att[0] = 0.0;  // 俯仰角（弧度）
		////NAV_Data_Full.SINS.att[1] = 0.0;  // 横滚角（弧度）
		//NAV_Data_Full.SINS.att[0] = atan2(NAV_Data_Full.IMU.acc_use[1], sqrt(NAV_Data_Full.IMU.acc_use[0] * NAV_Data_Full.IMU.acc_use[0] + NAV_Data_Full.IMU.acc_use[2] * NAV_Data_Full.IMU.acc_use[2]));//俯仰角
		//NAV_Data_Full.SINS.att[1] = atan2(-NAV_Data_Full.IMU.acc_use[0], NAV_Data_Full.IMU.acc_use[2]);//横滚角
		//NAV_Data_Full.SINS.att[2] = 0.0 * DEG2RAD;  // 航向角（弧度，0表示正北）
		//att2qnb(NAV_Data_Full.SINS.att, NAV_Data_Full.SINS.qnb);
		//Qnb2Cnb(NAV_Data_Full.SINS.qnb, NAV_Data_Full.SINS.Cb2n);
		// 执行SINS系统初始化
		SINS_Init_presins(&NAV_Data_Full);
		// 初始化完成后直接进入纯惯性导航状态
		if (NAV_Data_Full.SINS.Init_flag == 1)
		{
			SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
		}
		
	}
	break;
	case E_NAV_STATUS_SYSTEM_STANDARD://纯惯性导航状态
	{
		// ========== 地球参数更新 ==========
		// 根据当前位置和速度更新地球参数（重力、地球自转角速度等）
		Earth_UP(&NAV_Data_Full, NAV_Data_Full.SINS.pos, NAV_Data_Full.SINS.vn);

		// ========== 纯惯性导航更新 ==========
		// 使用不含磁力计的Mahony互补滤波算法进行姿态更新
		// 这种方法只使用加速度计和陀螺仪数据，适合纯惯性导航
		MahonyUpdate_NoMAG(&NAV_Data_Full);
		HeaveCompensation_Process(&NAV_Data_Full);  // 升沉补偿算法
	}
	break;
	default:
	{
		// 其他状态不做处理
		break;
	}
	}

	// ========== 输出数据更新 ==========
	// 输出ASCII协议数据，更新对外接口数据结构
	Out_Data_Up(&NAV_Data_Out); //输出数据更新
}




////////////////////////end/////////////////////////////////////////////////////
