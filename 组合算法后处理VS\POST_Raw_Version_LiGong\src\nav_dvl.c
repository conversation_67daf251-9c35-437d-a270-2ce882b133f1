/***********************************************************************************
nav dvl module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|AI Assistant   |  2025-1-13         | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/

#include "nav_dvl.h"
#include "nav_type.h"
#include "algorithm.h"
#include "nav_sins.h"
#include "nav_kf.h"

//#ifdef WIN32
//#include "inav_log.h"
//#endif

// DVL parameters
#define  R_DVL_true  DVL_VEL_VAR
#define  R_DVL_fake  VEL_VAR2
#define  Qk_DVL   (0*1.0e-10)
#define Q_DVL_gps_factor      (0*1.0e-12)
#define R_DVL_gps_factor_rtk  (0.2*DEG2RAD * 0.2*DEG2RAD)
#define R_DVL_gps_factor_spp  (0.4*DEG2RAD * 0.4*DEG2RAD)
#define Q_DVL_factor   (1.0e-10)
#define R_DVL_factor   (0.05*0.05)

/******************************************************************************
*原  型：void Get_DVL_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p)
*功  能：DVL数据接收和处理
*输  入：NAV_Data_Full_p, CombineData_p
*输  出：DVL数据处理结果
*******************************************************************************/
void Get_DVL_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p)
{
	double GPSvel = 0.0;
	//DVL data reception and processing
	NAV_Data_Full_p->DVL.dvl_flag = CombineData_p->dvlInfo.flag;
	NAV_Data_Full_p->DVL.dvl_caculat_flag = RETURN_FAIL;
	NAV_Data_Full_p->DVL.dvl_normal_flag = RETURN_SUCESS;
	//NAV_Data_Full_p->DVL.dvl_flag = RETURN_FAIL;//****JUST FOR TEST****
	if (NAV_Data_Full_p->DVL.dvl_flag)//DVL data available
	{
		NAV_Data_Full_p->DVL.counter_old = NAV_Data_Full_p->DVL.counter;
		if (NAV_Data_Full_p->DVL.counter_old != CombineData_p->dvlInfo.counter)
		{
			//counter
			NAV_Data_Full_p->DVL.counter = CombineData_p->dvlInfo.counter;
			//data
			NAV_Data_Full_p->DVL.DVL_SPEED_Front = CombineData_p->dvlInfo.data.DVL_SPEED_Front;
			NAV_Data_Full_p->DVL.DVL_SPEED_Right = CombineData_p->dvlInfo.data.DVL_SPEED_Right;
			NAV_Data_Full_p->DVL.DVL_SPEED_Up = CombineData_p->dvlInfo.data.DVL_SPEED_Up;
			//NAV_Data_Full_p->DVL.deltaT=CombineData_p->dvlInfo.deltaT;	
		}

		if (NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)
		{
			NAV_Data_Full_p->DVL.dvl_caculat_flag = RETURN_SUCESS;
			NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
			//NAV_Data_Full_p->IMU_Cnt=0;
		}
		else
		{
			if ((NAV_Data_Full_p->GPS.gpssecond - NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms >= SOURCE_UP_dTim_ms))
			{
				NAV_Data_Full_p->DVL.dvl_caculat_flag = RETURN_SUCESS;
				NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
			}
		}
		//DVL velocity processing
		if (NAV_Data_Full_p->DVL.dvl_caculat_flag == RETURN_SUCESS)
		{
			//Calculate DVL average velocity (forward velocity as main reference)
			NAV_Data_Full_p->DVL.DVL_SPEED_ave = NAV_Data_Full_p->DVL.DVL_SPEED_Front;
			
			//Calculate DVL angular velocity based on right velocity and vehicle width
			double L = 1.29; // Vehicle width in meters, adjust according to actual situation
			NAV_Data_Full_p->DVL.DVL_anglespeed = NAV_Data_Full_p->DVL.DVL_SPEED_Right / L;
			
			//DVL anomaly detection
			if (((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID) && (NAV_Data_Full_p->GPS.pdop < 4.0))
				|| ((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK) && (NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED))
				)
			{
				GPSvel = sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.vu * NAV_Data_Full_p->GPS.vu);
				if (fabs(GPSvel - NAV_Data_Full_p->DVL.DVL_SPEED_ave) > 0.6)
				{
					//NAV_Data_Full_p->DVL.dvl_normal_flag = RETURN_FAIL;
				}
			}
		}
	}
	else//No DVL data
	{
	}

}

/******************************************************************************
*原  型：double DVL_SpeedOptimize(double v_front, double v_right, double v_up, double scale_factor, unsigned char *pSpeed_valid_flag)
*功  能：DVL速度优化处理
*输  入：v_front, v_right, v_up, scale_factor, pSpeed_valid_flag
*输  出：优化后的DVL速度
*******************************************************************************/
double DVL_SpeedOptimize(double v_front, double v_right, double v_up, double scale_factor, unsigned char *pSpeed_valid_flag)
{
	double dvl_speed_optimized = 0.0;
	*pSpeed_valid_flag = 1;
	
	// Simple optimization: use forward velocity as primary
	dvl_speed_optimized = scale_factor * v_front;
	
	// Add validation logic here if needed
	if (fabs(v_front) > 10.0) // Maximum reasonable speed 10 m/s
	{
		*pSpeed_valid_flag = 0;
		dvl_speed_optimized = 0.0;
	}
	
	return dvl_speed_optimized;
}

/******************************************************************************
*原  型：void DVL_Scalar_KalmanFilte(double * x, double zk, double* P, double Q, double R, double MaxError)
*功  能：DVL标量卡尔曼滤波
*输  入：x, zk, P, Q, R, MaxError
*输  出：滤波后的状态
*******************************************************************************/
void DVL_Scalar_KalmanFilte(double * x, double zk, double* P, double Q, double R, double MaxError)
{
	double K = 0.0;
	double P_pred = 0.0;
	double innovation = 0.0;
	
	// Time update
	P_pred = *P + Q;
	
	// Measurement update
	innovation = zk - *x;
	
	// Limit innovation
	if (fabs(innovation) > MaxError)
	{
		if (innovation > 0)
			innovation = MaxError;
		else
			innovation = -MaxError;
	}
	
	// Kalman gain
	K = P_pred / (P_pred + R);
	
	// State update
	*x = *x + K * innovation;
	
	// Covariance update
	*P = (1.0 - K) * P_pred;
}

/******************************************************************************
*原  型：void DVL_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：基于SINS的DVL安装角度估计和标定
*输  入：NAV_Data_Full_p
*输  出：DVL scale_factor and installation angles
*******************************************************************************/
void DVL_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i,j,k;
	double vb_sins[3] = {0};

	//DVL SubKF parameters
	double SubKF_Pk_[3*3]={0};
	double SubKF_Kg[3*3]={0};
	double misR[3]={0};
	double mis_Zk[3]={0};
	double mis_Zktmp[3]={0};
	double Mea_dvl[3]={0,0,0}; // DVL measurement in different directions
	double Kg_z[3]={0};

	double Hk[3*3]={0};
	double Hk_T[3*3]={0};
	double PHk_T[3*3]={0};

	double askew_vb[3*3]={0};
	double HPH_T_R[3*3]={0};
	double HPH_T_R_inv[3*3]={0};
	double Cbm[3*3]={0};
	double R_tmp=0.0;

	double KgH[3*3]={0};
	double KgHP[3*3]={0};

	double dq[4]={0};
	double normQ=0.0;
	double V_fake=0.0;
	double yaw_vel=0.0;
	double dvl_and_GPS=0.0;

	double Vim[3]={0};
	double Z_k=0.0;

	// Calculate body frame velocity from SINS
	matmul("TN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.vn, 0.0, vb_sins);
	// For DVL, we work directly in body frame, so no need for additional transformation
	// mis_Zktmp is the expected DVL measurement in body frame
	mis_Zktmp[0] = vb_sins[0];  // Forward velocity in body frame
	mis_Zktmp[1] = vb_sins[1];  // Right velocity in body frame
	mis_Zktmp[2] = vb_sins[2];  // Up velocity in body frame

	// DVL calibration processing
	if (sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn) > 0.5
		&& ((NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED) || (NAV_Data_Full_p->GPS.NO_RTK_heading_flag))
		&& (NAV_Data_Full_p->Nav_Status == E_NAV_STATUS_SYSTEM_STANDARD)
	   )
	{
		NAV_Data_Full_p->Subkf2_Cnt++;

		// DVL installation angle estimation using SubKF
		{
			V_fake = sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.vu * NAV_Data_Full_p->GPS.vu);
			yaw_vel = -atan2(NAV_Data_Full_p->GPS.ve, NAV_Data_Full_p->GPS.vn) * RAD2DEG;
			dvl_and_GPS = (yaw_vel - NAV_Data_Full_p->GPS.Heading_cor);
			if (!(fabs(dvl_and_GPS) < 30))
			{
				V_fake = -V_fake;
			}

			if (NAV_Data_Full_p->DVL.dvl_caculat_flag && NAV_Data_Full_p->DVL.dvl_normal_flag)
			{
				R_tmp = R_DVL_true;
				// DVL measures 3-axis velocity in body frame
				Mea_dvl[0] = NAV_Data_Full_p->DVL.DVL_SPEED_Front; // Forward velocity
				Mea_dvl[1] = NAV_Data_Full_p->DVL.DVL_SPEED_Right; // Right velocity
				Mea_dvl[2] = NAV_Data_Full_p->DVL.DVL_SPEED_Up;    // Up velocity
			}
			else
			{
				R_tmp = R_DVL_fake;
				// Use GPS velocity projected to body frame as fake measurement
				Mea_dvl[0] = V_fake;  // Assume forward motion
				Mea_dvl[1] = 0.0;     // No lateral motion
				Mea_dvl[2] = 0.0;     // No vertical motion
			}

			// Set measurement noise
			misR[0] = DVL_VEL_VAR;
			misR[1] = R_tmp;
			misR[2] = DVL_VEL_VAR;

			// Increase noise during turning
			if (NAV_Data_Full_p->TurnningFlag)
			{
				misR[0] = 4 * DVL_VEL_VAR;
				misR[1] = 4 * R_tmp;
				misR[2] = 4 * DVL_VEL_VAR;
			}

			// For DVL, H matrix is identity since we measure body frame velocity directly
			// H = I (3x3 identity matrix) for the velocity states
			Hk[0] = 1.0; Hk[1] = 0.0; Hk[2] = 0.0;
			Hk[3] = 0.0; Hk[4] = 1.0; Hk[5] = 0.0;
			Hk[6] = 0.0; Hk[7] = 0.0; Hk[8] = 1.0;
			Mat_Tr(3, Hk, Hk_T);

			mis_Zk[0] = mis_Zktmp[0] - Mea_dvl[0];
			mis_Zk[1] = mis_Zktmp[1] - Mea_dvl[1];
			mis_Zk[2] = mis_Zktmp[2] - Mea_dvl[2];

			// Time update
			for (i = 0; i < 3; i++)
			{
				for (j = 0; j < 3; j++)
				{
					if (i == j)
						SubKF_Pk_[i + 3 * j] = NAV_Data_Full_p->SubKF.Pk[i + 3 * j] + Qk_DVL;
					else
						SubKF_Pk_[i + 3 * j] = NAV_Data_Full_p->SubKF.Pk[i + 3 * j];
				}
			}

			matmul("NN", 3, 3, 3, 1.0, SubKF_Pk_, Hk_T, 0.0, PHk_T);
			matmul("NN", 3, 3, 3, 1.0, Hk, PHk_T, 0.0, HPH_T_R);

			// HPH_T+R
			HPH_T_R[0 + 3 * 0] = HPH_T_R[0 + 3 * 0] + misR[0];
			HPH_T_R[1 + 3 * 1] = HPH_T_R[1 + 3 * 1] + misR[1];
			HPH_T_R[2 + 3 * 2] = HPH_T_R[2 + 3 * 2] + misR[2];

			Mat3_Inv(HPH_T_R, HPH_T_R_inv);
			matmul("NN", 3, 3, 3, 1.0, PHk_T, HPH_T_R_inv, 0.0, SubKF_Kg);
			matmul("NN", 3, 1, 3, 1.0, SubKF_Kg, mis_Zk, 0.0, Kg_z);

			// Measurement update
			NAV_Data_Full_p->SubKF.Xk[0] = Kg_z[0];
			NAV_Data_Full_p->SubKF.Xk[1] = Kg_z[1];
			NAV_Data_Full_p->SubKF.Xk[2] = Kg_z[2];

			matmul("NN", 3, 3, 3, 1.0, SubKF_Kg, Hk, 0.0, KgH);
			matmul("NN", 3, 3, 3, -1.0, KgH, SubKF_Pk_, 0.0, KgHP);
			matrixSum(SubKF_Pk_, KgHP, 3, 3, 1.0, NAV_Data_Full_p->SubKF.Pk);

			// Symmetrize covariance matrix
			symmetry(NAV_Data_Full_p->SubKF.Pk, 3, NAV_Data_Full_p->SubKF.Pk);

			// Feedback
			qdelphi(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Xk, NAV_Data_Full_p->SubKF.Q_b2m);

			// Quaternion normalization
			normQ = sqrt(NAV_Data_Full_p->SubKF.Q_b2m[0] * NAV_Data_Full_p->SubKF.Q_b2m[0]
				+ NAV_Data_Full_p->SubKF.Q_b2m[1] * NAV_Data_Full_p->SubKF.Q_b2m[1]
				+ NAV_Data_Full_p->SubKF.Q_b2m[2] * NAV_Data_Full_p->SubKF.Q_b2m[2]
				+ NAV_Data_Full_p->SubKF.Q_b2m[3] * NAV_Data_Full_p->SubKF.Q_b2m[3]
			);
			NAV_Data_Full_p->SubKF.Q_b2m[0] = NAV_Data_Full_p->SubKF.Q_b2m[0] / normQ;
			NAV_Data_Full_p->SubKF.Q_b2m[1] = NAV_Data_Full_p->SubKF.Q_b2m[1] / normQ;
			NAV_Data_Full_p->SubKF.Q_b2m[2] = NAV_Data_Full_p->SubKF.Q_b2m[2] / normQ;
			NAV_Data_Full_p->SubKF.Q_b2m[3] = NAV_Data_Full_p->SubKF.Q_b2m[3] / normQ;

			Qnb2Cnb(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Cmb);
			qnb2att(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.att_xyz);
			NAV_Data_Full_p->DVL.att_dvl2_b_filte[0] = NAV_Data_Full_p->SubKF.att_xyz[0] * RAD2DEG;
			NAV_Data_Full_p->DVL.att_dvl2_b_filte[1] = NAV_Data_Full_p->SubKF.att_xyz[1] * RAD2DEG;
			NAV_Data_Full_p->DVL.att_dvl2_b_filte[2] = NAV_Data_Full_p->SubKF.att_xyz[2] * RAD2DEG;
			NAV_Data_Full_p->SubKF.Xk[0] = 0;
			NAV_Data_Full_p->SubKF.Xk[1] = 0;
			NAV_Data_Full_p->SubKF.Xk[2] = 0;
		}
	}
	else
	{
		NAV_Data_Full_p->Subkf2_Cnt = 0;
	}

	// DVL scale factor estimation
	{
		// Use forward velocity for scale factor estimation (primary motion direction)
		if(RETURN_SUCESS==NAV_Data_Full_p->DVL.dvl_caculat_flag && fabs(NAV_Data_Full_p->DVL.DVL_SPEED_Front)>0.5)
		{
			double R_dvl_tmp=R_DVL_factor;
			if(NAV_Data_Full_p->TurnningFlag)
			{
				 R_dvl_tmp=4*R_DVL_factor;
			}
			R_dvl_tmp = R_dvl_tmp / fabs(NAV_Data_Full_p->DVL.DVL_SPEED_Front)/ fabs(NAV_Data_Full_p->DVL.DVL_SPEED_Front);
			// Use forward velocity component for scale factor estimation
			NAV_Data_Full_p->DVL.scale_factor_dvl = mis_Zktmp[0]/NAV_Data_Full_p->DVL.DVL_SPEED_Front;
	        DVL_Scalar_KalmanFilte(&NAV_Data_Full_p->DVL.scale_factor_dvl_filte, NAV_Data_Full_p->DVL.scale_factor_dvl, &NAV_Data_Full_p->DVL.P_dvl_fact, 1.0*Q_DVL_factor, R_dvl_tmp,0.15);
		}
		else
		{
#ifdef WIN32
			//inav_log(INAVMD(LOG_ERR),"DVL Speed=[%f,%f,%f](m/s)",
			//NAV_Data_Full_p->DVL.DVL_SPEED_Front,
			//NAV_Data_Full_p->DVL.DVL_SPEED_Right,
			//NAV_Data_Full_p->DVL.DVL_SPEED_Up);
#endif
		}
	}
}
