/***********************************************************************************
nav dvl module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|AI Assistant   |  2025-1-13         | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/

#ifndef __NAV_DVL_H_
#define __NAV_DVL_H_

#include "nav_type.h"
#include "algorithm.h"

// DVL related function declarations
void Get_DVL_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p);
void DVL_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p);
double DVL_SpeedOptimize(double v_front, double v_right, double v_up, double scale_factor, unsigned char *pSpeed_valid_flag);
void DVL_Scalar_KalmanFilte(double * x, double zk, double* P, double Q, double R, double MaxError);

// DVL Kalman filter parameters
#define P_DVL_Cvb_ (10.0)*(10.0)
#define Q_DVL_Cvb_   ((0.001)*(0.001)*0.0001)
#define R_DVL_Cvb_   ((0.5)*(0.5)*0.01)

#define Q_DVL_factor   (1.0e-10)
#define R_DVL_factor   (0.05*0.05)

// DVL measurement noise parameters
#define  R_DVL_true  DVL_VEL_VAR2
#define  R_DVL_fake  VEL_VAR2
#define  Qk_DVL   (0*1.0e-10)

// DVL installation parameters
#define Q_DVL_gps_factor      (0*1.0e-12)
#define R_DVL_gps_factor_rtk  (0.2*DEG2RAD * 0.2*DEG2RAD)
#define R_DVL_gps_factor_spp  (0.4*DEG2RAD * 0.4*DEG2RAD)

#endif
