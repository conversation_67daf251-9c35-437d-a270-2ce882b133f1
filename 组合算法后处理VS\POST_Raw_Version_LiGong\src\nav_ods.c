/***********************************************************************************
nav ods module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
//
#define P_Cvb_ (10.0)*(10.0)
#define Q_Cvb_   ((0.001)*(0.001)*0.0001)
#define R_Cvb_   ((0.5)*(0.5)*0.01)
//#define R_Cvb_ (8.0f)*(8.0f)
//#define R_Cvb_ (4.0f)*(4.0f)
double P_Cvb[3] = { P_Cvb_,P_Cvb_,P_Cvb_ };
double Q_Cvb[3] = { Q_Cvb_,Q_Cvb_,Q_Cvb_ };
double R_Cvb[3] = { R_Cvb_,R_Cvb_,R_Cvb_ };

//#define P_wheel_factor   (0.15*0.15)
#define Q_wheel_factor   (1.0e-10)//1e-12
//double R_wheel_factor = (1.0f)*(1.0f);
#define R_wheel_factor   (0.05*0.05)
//double scale_factor_old = 0;
unsigned char valid_wheelspeed = RETURN_SUCESS;
//*************20240713*******Guolong Zhang***************
#define  R_odo_true  WHEEL_VEL_VAR2
#define  R_odo_fake  VEL_VAR2
//#define  R_odo_angw       (2.0*WHEEL_VEL_VAR2/1.29/1.29)
//#define  R_odo_angw_res   (1.5*DEG2RAD * 1.5*DEG2RAD)
//double   R_odo_angw_ALL[3] = { R_odo_angw_res,R_odo_angw_res,R_odo_angw };
double   mis_R[3] = { 0.0 };
#define  Qk_odo   (0*1.0e-10)
//*******b2gps****
//double P_gps_factor = 1 * 1;
#define Q_gps_factor      (0*1.0e-12)    //((0.1) * (0.1) * 0.0);//
#define R_gps_factor_rtk  (0.2*DEG2RAD * 0.2*DEG2RAD) //****1m????*****
#define R_gps_factor_spp  (0.4*DEG2RAD * 0.4*DEG2RAD) 
/******************************************************************************
*?  ???Caculate_ODS_angle(_NAV_Data_Full_t* NAV_Data_Full_p)
*??  ?????????????????????????????
*??  ??
*??  ?????????????
*******************************************************************************/
double  Caculate_ODS_angle(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	double L = 1.29;//???????min????????????????129cm
	double Wheel_Left, Wheel_Right,ODS_angle;
	Wheel_Left = NAV_Data_Full_p->ODS.WheelSpeed_Back_Left * KMPH2MPS;
	Wheel_Right = NAV_Data_Full_p->ODS.WheelSpeed_Back_Right * KMPH2MPS;
	
	//ODS_angle = (Wheel_Right - Wheel_Left) / L;
	ODS_angle = (Wheel_Right - Wheel_Left) / L;//*********
	return ODS_angle;
}
/******************************************************************************
*?  ???static void Scalar_KalmanFilte(double * x,float zk,double* P,double Q,double R)
*??  ??????????????????H Phi???????
*??  ?? 
*??  ??????,10??????????
*******************************************************************************/
void Scalar_KalmanFilte(double * x,double zk,double* P,double Q,double R,double MaxError)
{
	double K = 0.0f;
	double x_ = 0.0f;
	double P_ = 0.0f;
	double err = 0;
	x_ = *x;
	P_ = *P+Q;
	K = P_/(P_+R);
	err = zk-x_;
	//??????
	if(err>MaxError)
	{
		err = MaxError;
	}
	else if(err<-MaxError)
	{
		err = -MaxError;
	}
#if 0	
	//??????
	if(err>1*DEG2RAD)
	{
		err = 1*DEG2RAD;
	}
	else if(err<-1*DEG2RAD)
	{
		err = -1*DEG2RAD;
	}
#endif	
	*x = x_+K*(zk-x_);
//	*x = x_+K*err;
	*P = P_*(1.0f-K);
}	

//???20??????????
double WheelSpeedOptimize(double v0,double v1,double v2,double v3,double scale_factor,unsigned char *pSpeed_valid_flag)
{
//	int i=0;
//	double vAve=0.0;
//	double dv[4]={0.0};
//	double d_v_wheel_max=0.0;
//	int d_v_wheel_max_index=0;
//	double v_wheel[4]={v0,v1,v2,v3};
//	unsigned char flag_v_wheel[4]={1,1,1,1}; 
	double v_wheel_ave=0.0;
//	unsigned char num_vaild_v_wheel=0;

	//*pSpeed_valid_flag = RETURN_SUCESS;
	//****************2 BACKWARD WHEEL ONLY***************
	//v_wheel_ave=scale_factor*(v2+v3)/2;
	v_wheel_ave=(v2+v3)/2;
	return   v_wheel_ave;//scale_factor*v_wheel_ave;
	//*******************************
	#if 0
	//delete abnormal wheel v
	for(i=0;i<4;i++)
	{
		if(v_wheel[i]<0 ||v_wheel[i]> MAX_WHEEL_VEL)
		{
			flag_v_wheel[i]=0;
		}
		else
		{
			num_vaild_v_wheel++;
			v_wheel_ave+=v_wheel[i];
		}
	}

	//???????????????????????????????
	if( 0 == num_vaild_v_wheel)
	{
		*pSpeed_valid_flag=RETURN_FAIL;
	}
	//???????????1-2?????????
	else if(num_vaild_v_wheel<=2 && num_vaild_v_wheel>0)
	{
		v_wheel_ave=v_wheel_ave/num_vaild_v_wheel;
		return scale_factor*v_wheel_ave;
	}
	//?????????3-4????????????????????????
	else
	{
		vAve = v_wheel_ave/num_vaild_v_wheel;
		v_wheel_ave = 0;
		for(i = 0;i<4;i++)
		{
			if(1 == flag_v_wheel[i])
			{
				dv[i]=fabs(v_wheel[i]-vAve);
			}
		}
		 for(i = 0;i<4;i++)
		 {
		 	if((1 == flag_v_wheel[i]) && (dv[i] >= d_v_wheel_max))
			{
				d_v_wheel_max 		= dv[i];
				d_v_wheel_max_index = i;
			}
		}
		 for(i = 0;i<4;i++)
    		{
			if((1 == flag_v_wheel[i]) &&(i != d_v_wheel_max_index))
			{
				v_wheel_ave +=v_wheel[i];
			}
		}
		 v_wheel_ave = v_wheel_ave/(num_vaild_v_wheel-1);
		return scale_factor*v_wheel_ave;	
	}

	return scale_factor*v_wheel_ave;
		
#if 0	
	vAve = 0.25f*(v_wheel[0]+v_wheel[1]+v_wheel[2]+v_wheel[3]);	
	for(i = 0;i<4;i++)
	{
		dv[i]=fabs(v_wheel[i]-vAve);
	}

    for(i = 0;i<4;i++)
    {
		if (dv[i] >= d_v_wheel_max)
		{
			d_v_wheel_max 		= dv[i];
			d_v_wheel_max_index = i;
		}
	}

	for(i = 0;i<4;i++)
    {
		if(i != d_v_wheel_max_index)
		{
			v_wheel_ave += (0.3333333f*v_wheel[i]);
		}
	}
	return scale_factor*v_wheel_ave;
#endif	
	#endif
}


void Get_DVL_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p)
{
	double GPSvel = 0.0;
	//DVL data reception and processing
	NAV_Data_Full_p->DVL.dvl_flag = CombineData_p->dvlInfo.flag;
	NAV_Data_Full_p->DVL.dvl_caculat_flag = RETURN_FAIL;
	NAV_Data_Full_p->DVL.dvl_normal_flag = RETURN_SUCESS;
	//NAV_Data_Full_p->DVL.dvl_flag = RETURN_FAIL;//****JUST FOR TEST****
	if (NAV_Data_Full_p->DVL.dvl_flag)//DVL data available
	{
		NAV_Data_Full_p->DVL.counter_old = NAV_Data_Full_p->DVL.counter;
		if (NAV_Data_Full_p->DVL.counter_old != CombineData_p->dvlInfo.counter)
		{
			//counter
			NAV_Data_Full_p->DVL.counter = CombineData_p->dvlInfo.counter;
			//data
			NAV_Data_Full_p->DVL.DVL_SPEED_Front = CombineData_p->dvlInfo.data.DVL_SPEED_Front;
			NAV_Data_Full_p->DVL.DVL_SPEED_Right = CombineData_p->dvlInfo.data.DVL_SPEED_Right;
			NAV_Data_Full_p->DVL.DVL_SPEED_Up = CombineData_p->dvlInfo.data.DVL_SPEED_Up;
			//NAV_Data_Full_p->DVL.deltaT=CombineData_p->dvlInfo.deltaT;
		}

		if (NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)
		{
			NAV_Data_Full_p->DVL.dvl_caculat_flag = RETURN_SUCESS;
			NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
			//NAV_Data_Full_p->IMU_Cnt=0;
		}
		else
		{
			if ((NAV_Data_Full_p->GPS.gpssecond - NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms >= SOURCE_UP_dTim_ms))
			{
				NAV_Data_Full_p->DVL.dvl_caculat_flag = RETURN_SUCESS;
				NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
			}
		}
		//DVL velocity processing
		if (NAV_Data_Full_p->DVL.dvl_caculat_flag == RETURN_SUCESS)
		{
			//Calculate DVL average velocity (forward velocity as main reference)
			NAV_Data_Full_p->DVL.DVL_SPEED_ave = NAV_Data_Full_p->DVL.DVL_SPEED_Front;

			//Calculate DVL angular velocity based on right velocity and vehicle width
			double L = 1.29; // Vehicle width in meters, adjust according to actual situation
			NAV_Data_Full_p->DVL.DVL_anglespeed = NAV_Data_Full_p->DVL.DVL_SPEED_Right / L;

			//DVL anomaly detection
			if (((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID) && (NAV_Data_Full_p->GPS.pdop < 4.0))
				|| ((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK) && (NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED))
				)
			{
				GPSvel = sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.vu * NAV_Data_Full_p->GPS.vu);
				if (fabs(GPSvel - NAV_Data_Full_p->DVL.DVL_SPEED_ave) > 0.6)
				{
					//NAV_Data_Full_p->DVL.dvl_normal_flag = RETURN_FAIL;
				}
			}
		}
	}
	else//No DVL data
	{
	}

}




void Get_ODS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	double GPSvel = 0.0;
	//????????can???
	NAV_Data_Full_p->ODS.ods_flag = CombineData_p->canInfo.flag;
	NAV_Data_Full_p->ODS.ods_caculat_flag = RETURN_FAIL;
	NAV_Data_Full_p->ODS.ods_normal_flag = RETURN_SUCESS;
	//NAV_Data_Full_p->ODS.ods_flag = RETURN_FAIL;//****JUST FOR TEST****
	if (NAV_Data_Full_p->ODS.ods_flag)//*********?????????****
	{
		NAV_Data_Full_p->ODS.counter_old = NAV_Data_Full_p->ODS.counter;
		if (NAV_Data_Full_p->ODS.counter_old != CombineData_p->canInfo.counter)
		{
			//counter
			NAV_Data_Full_p->ODS.counter = CombineData_p->canInfo.counter;
			//data
			NAV_Data_Full_p->ODS.WheelSpeed_Front_Left = CombineData_p->canInfo.data.WheelSpeed_Front_Left;
			NAV_Data_Full_p->ODS.WheelSpeed_Front_Right = CombineData_p->canInfo.data.WheelSpeed_Front_Right;
			NAV_Data_Full_p->ODS.WheelSpeed_Back_Left = CombineData_p->canInfo.data.WheelSpeed_Back_Left;
			NAV_Data_Full_p->ODS.WheelSpeed_Back_Right = CombineData_p->canInfo.data.WheelSpeed_Back_Right;
			NAV_Data_Full_p->ODS.WheelSteer = CombineData_p->canInfo.data.WheelSteer;
			NAV_Data_Full_p->ODS.OdoPulse_1 = CombineData_p->canInfo.data.OdoPulse_1;
			NAV_Data_Full_p->ODS.OdoPulse_2 = CombineData_p->canInfo.data.OdoPulse_2;
			NAV_Data_Full_p->ODS.Gear = CombineData_p->canInfo.data.Gear;
			//NAV_Data_Full_p->ODS.deltaT=CombineData_p->canInfo.deltaT;	
		}

		if (NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)
		{
			NAV_Data_Full_p->ODS.ods_caculat_flag = RETURN_SUCESS;
			NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
			//NAV_Data_Full_p->IMU_Cnt=0;
		}
		else
		{
			if ((NAV_Data_Full_p->GPS.gpssecond - NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms >= SOURCE_UP_dTim_ms))
			{
				NAV_Data_Full_p->ODS.ods_caculat_flag = RETURN_SUCESS;
				NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
			}
		}
		//**********???????*****->********
		if (NAV_Data_Full_p->ODS.ods_caculat_flag == RETURN_SUCESS)
		{
			//NAV_Data_Full_p->ODS.ods_W_flag = RETURN_SUCESS;//***********
			NAV_Data_Full_p->ODS.ODS_anglespeed = Caculate_ODS_angle(NAV_Data_Full_p);
			NAV_Data_Full_p->ODS.WheelSpeed_ave = WheelSpeedOptimize(NAV_Data_Full_p->ODS.WheelSpeed_Front_Left * KMPH2MPS,
				NAV_Data_Full_p->ODS.WheelSpeed_Front_Right * KMPH2MPS,
				NAV_Data_Full_p->ODS.WheelSpeed_Back_Left * KMPH2MPS,
				NAV_Data_Full_p->ODS.WheelSpeed_Back_Right * KMPH2MPS,
				NAV_Data_Full_p->ODS.scale_factor_filte,// scale_factor_filte;scale_factor
				&valid_wheelspeed);
			//******20241230********ODO?????**->*****
			if (((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID) && (NAV_Data_Full_p->GPS.pdop < 4.0))
				|| ((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK) && (NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED))
				)
			{
				GPSvel = sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.vu * NAV_Data_Full_p->GPS.vu);
				if (fabs(GPSvel - NAV_Data_Full_p->ODS.WheelSpeed_ave) > 0.6)
				{
					//NAV_Data_Full_p->ODS.ods_normal_flag = RETURN_FAIL;
				}
			}
			//*******20241230*******ODO?????**<-*****
				//**********
			if (NAV_Data_Full_p->ODS.Gear == E_ODS_GEAR_BACKWARD)
			{
				NAV_Data_Full_p->ODS.ODS_anglespeed = -NAV_Data_Full_p->ODS.ODS_anglespeed;
				NAV_Data_Full_p->ODS.WheelSpeed_ave = -NAV_Data_Full_p->ODS.WheelSpeed_ave;
			}
			else if (E_ODS_GEAR_STATIC == NAV_Data_Full_p->ODS.Gear)
			{
				//wheel_speed= 0;
			}
			else if (E_ODS_GEAR_FORWARD == NAV_Data_Full_p->ODS.Gear)
			{
			}
			else
			{
#ifdef linux
				inav_log(INAVMD(LOG_ERR), " error ODS.Gear =%d", NAV_Data_Full_p->ODS.Gear);
#endif			
				//wheel_speed= 0;			
			}
			}
		//**********???????*****<-********
		}
	else//*****??????****
	{
	}

}

/******************************************************************************
*?  ???void ODS_Angle_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
*??  ???????SINS???????????????????
*??  ?? NAV_Data_Full_p
*??  ????scale_factor
*******************************************************************************/
//??????????????? ???????????NAV_Data_Full_p->ODS.scale_factor=vb/v_wheel
void ODS_Angle_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i,j,k;
	double vb_sins[3] = {0};
//	float v_wheel[4] = {0};
//	//float v_wheel_ave = 0;
//	double v_wheel_ave = 0;
//	double angle_wheel_ave = 0;
//	float d_v_wheel[4] = {0};
//	float d_v_wheel_max[2] = {0};
	//unsigned char valid_wheelspeed=RETURN_SUCESS;
//	double att_Cvb_Obs[3]  = {0,0,0};
	
	//double temp,temp1,temp2,temphead;
	//double temp1,temp2,temphead;
	//double vn_heading=0;
				//********subKF para*****
	 
			double SubKF_Pk_[3*3]={0};
			double SubKF_Kg[3*3]={0};
			double misR[3]={0};
			double mis_Zk[3]={0};
			double mis_Zktmp[3]={0};
			double Mea_odo[3]={0,0,0};//*******?????????????????????***
			double Kg_z[3]={0};
			
			double Hk[3*3]={0};
			double Hk_T[3*3]={0};
			double PHk_T[3*3]={0};
			
		    double Hkw[3*3]={0};
			double Hkw_T[3*3]={0};
			double PHkw_T[3*3]={0};
			double askew_wb[3*3]={0};
			double wb[3]={0,0,0};
			double SubKF_w_Kg[3*3]={0};
			double HPH_T_w_R[3*3]={0};
			double HPH_T_w_R_inv[3*3]={0};
			double mis_w_Zk[3]={0,0,0};
			double HXk[3]={0};
			double Z_HXk[3]={0};
			double KgZ_HXk[3]={0};
			double KgH_w[3*3]={0};
			double KgHP_w[3*3]={0};
									
			double askew_vb[3*3]={0};
			double HPH_T_R[3*3]={0};
			double HPH_T_R_inv[3*3]={0};
			double Cbm[3*3]={0};
			double R_tmp=0.0;
			
			double KgH[3*3]={0};
			double KgHP[3*3]={0};
			
			double dq[4]={0};
			double normQ=0.0;
			double V_fake=0.0;
			double  yaw_vel=0.0;
			double  ods_and_GPS=0.0;
	//*************************
			double Vim[3]={0};
			double Z_k=0.0;
	//**********????****
	//????????????????
			matmul("TN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.vn, 0.0, vb_sins);
			Mat_Tr(3, NAV_Data_Full_p->SubKF.Cmb, Cbm);
			matmul("NN", 3, 1, 3, 1.0, Cbm, vb_sins, 0.0, mis_Zktmp);
			//printf("standart=%f,%f,%f,%f,%f\n", NAV_Data_Full_p->SubKF.att_xyz[0] * RAD2DEG, NAV_Data_Full_p->SubKF.att_xyz[1] * RAD2DEG, NAV_Data_Full_p->SubKF.att_xyz[2] * RAD2DEG, NAV_Data_Full_p->SubKF.att_b2gps[2] * RAD2DEG, NAV_Data_Full_p->ODS.scale_factor_filte);
	       //???????????????
			if (sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn) > 0.5
				&& ((NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED) || (NAV_Data_Full_p->GPS.NO_RTK_heading_flag))//heading??????SPP??RTK??
				&& (NAV_Data_Full_p->Nav_Status == E_NAV_STATUS_SYSTEM_STANDARD)
			   )
			{
				NAV_Data_Full_p->Subkf2_Cnt++;//0.2s????

				//double vb_sins4 = vb_sins[1] * vb_sins[1] * vb_sins[1] * vb_sins[1];
				//************************
		//		if((sqrt(NAV_Data_Full_p->GPS.ve*NAV_Data_Full_p->GPS.ve+NAV_Data_Full_p->GPS.vn*NAV_Data_Full_p->GPS.vn)>2.0) 
		//		     &&(NAV_Data_Full_p->GPS.rtkStatus 		== E_GPS_RTK_FIXED)
		//		     &&(fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG)<12*MIN_TURN_GYRO_VALUE)
		//		     &&(NAV_Data_Full_p->GPS.headingStatus		== E_GPS_RTK_FIXED)
		//		  )
				//{
				
				/*
				 temp1 =-vb_sins[2]/vb_sins[1];// (-vb_sins[2]/sqrt(vb_sins[0]*vb_sins[0]+vb_sins[1]*vb_sins[1]));//tanx  misalign pitch
			  temp2 = (vb_sins[0]/vb_sins[1]); //tany   misalign yaw
				NAV_Data_Full_p->ODS.att_ods2_b[0] = (temp1)*RAD2DEG;
				NAV_Data_Full_p->ODS.att_ods2_b[1] = 0;
				NAV_Data_Full_p->ODS.att_ods2_b[2] = (temp2) * RAD2DEG;// - NAV_Data_Full_p->SINS.wnb[2]/2 *RAD2DEG * TS;
					Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[0],NAV_Data_Full_p->ODS.att_ods2_b[0],&P_Cvb[0],Q_Cvb[0]*0.0005,R_Cvb[0]/vb_sins4,20.0*DEG2RAD);
					Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[2],NAV_Data_Full_p->ODS.att_ods2_b[2],&P_Cvb[2],Q_Cvb[2],R_Cvb[2],20*DEG2RAD);
				*/
				//*******subkf********************************Guolong Zhang*******20240715*********************************->**********
				//************1*****???????????**********   
				//if (((NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED) || (NAV_Data_Full_p->GPS.NO_RTK_heading_flag))
				//	&&1
				//	)
				{
					V_fake = sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.vu * NAV_Data_Full_p->GPS.vu);
					yaw_vel = -atan2(NAV_Data_Full_p->GPS.ve, NAV_Data_Full_p->GPS.vn) * RAD2DEG;//****deg***
					ods_and_GPS = (yaw_vel - NAV_Data_Full_p->GPS.Heading_cor);
					if (!(fabs(ods_and_GPS) < 30))//*****????????*********
					{
						V_fake = -V_fake;
					}
					//
					if (NAV_Data_Full_p->ODS.ods_caculat_flag && NAV_Data_Full_p->ODS.ods_normal_flag)//*******????????????*****
					{
						R_tmp = R_odo_true;
						Mea_odo[1] = NAV_Data_Full_p->ODS.WheelSpeed_ave;
					}
					else//*******??????***
					{
						R_tmp = R_odo_fake;
						Mea_odo[1] = V_fake;
					}
					//**************
					//**************
					mis_R[0] = WHEEL_VEL_VAR;
					mis_R[1] = R_tmp;
					mis_R[2] = WHEEL_VEL_VAR;
					//*********??????******
					if (NAV_Data_Full_p->TurnningFlag)
					{
						mis_R[0] = 4 * WHEEL_VEL_VAR;
						mis_R[1] = 4 * R_tmp;
						mis_R[2] = 4 * WHEEL_VEL_VAR;
					}
					askew(vb_sins, askew_vb);
					matmul("NN", 3, 3, 3, -1.0, Cbm, askew_vb, 0.0, Hk);
					Mat_Tr(3, Hk, Hk_T);

					mis_Zk[0] = mis_Zktmp[0] - Mea_odo[0];
					mis_Zk[1] = mis_Zktmp[1] - Mea_odo[1];
					mis_Zk[2] = mis_Zktmp[2] - Mea_odo[2];
					//******time update******
					for (i = 0; i < 3; i++)
					{
						for (j = 0; j < 3; j++)
						{
							if (i == j)
								SubKF_Pk_[i + 3 * j] = NAV_Data_Full_p->SubKF.Pk[i + 3 * j] + Qk_odo;
							else
								SubKF_Pk_[i + 3 * j] = NAV_Data_Full_p->SubKF.Pk[i + 3 * j];
						}
					}
					//
					matmul("NN", 3, 3, 3, 1.0, SubKF_Pk_, Hk_T, 0.0, PHk_T);
					matmul("NN", 3, 3, 3, 1.0, Hk, PHk_T, 0.0, HPH_T_R);
					//HPH_T+R	
					HPH_T_R[0 + 3 * 0] = HPH_T_R[0 + 3 * 0] + mis_R[0];
					HPH_T_R[1 + 3 * 1] = HPH_T_R[1 + 3 * 1] + mis_R[1];
					HPH_T_R[2 + 3 * 2] = HPH_T_R[2 + 3 * 2] + mis_R[2];

					Mat3_Inv(HPH_T_R, HPH_T_R_inv);
					matmul("NN", 3, 3, 3, 1.0, PHk_T, HPH_T_R_inv, 0.0, SubKF_Kg);
					matmul("NN", 3, 1, 3, 1.0, SubKF_Kg, mis_Zk, 0.0, Kg_z);
					//*****mea update**1******
					NAV_Data_Full_p->SubKF.Xk[0] = Kg_z[0];
					NAV_Data_Full_p->SubKF.Xk[1] = Kg_z[1];
					NAV_Data_Full_p->SubKF.Xk[2] = Kg_z[2];

					matmul("NN", 3, 3, 3, 1.0, SubKF_Kg, Hk, 0.0, KgH);
					matmul("NN", 3, 3, 3, -1.0, KgH, SubKF_Pk_, 0.0, KgHP);
					matrixSum(SubKF_Pk_, KgHP, 3, 3, 1.0, NAV_Data_Full_p->SubKF.Pk);  //???????

					//Pk???? 
					symmetry(NAV_Data_Full_p->SubKF.Pk, 3, NAV_Data_Full_p->SubKF.Pk);//********
					//******feedback*****
					qdelphi(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Xk, NAV_Data_Full_p->SubKF.Q_b2m);
					//****??????????***
					normQ = sqrt(NAV_Data_Full_p->SubKF.Q_b2m[0] * NAV_Data_Full_p->SubKF.Q_b2m[0]
						+ NAV_Data_Full_p->SubKF.Q_b2m[1] * NAV_Data_Full_p->SubKF.Q_b2m[1]
						+ NAV_Data_Full_p->SubKF.Q_b2m[2] * NAV_Data_Full_p->SubKF.Q_b2m[2]
						+ NAV_Data_Full_p->SubKF.Q_b2m[3] * NAV_Data_Full_p->SubKF.Q_b2m[3]
					);
					NAV_Data_Full_p->SubKF.Q_b2m[0] = NAV_Data_Full_p->SubKF.Q_b2m[0] / normQ;
					NAV_Data_Full_p->SubKF.Q_b2m[1] = NAV_Data_Full_p->SubKF.Q_b2m[1] / normQ;
					NAV_Data_Full_p->SubKF.Q_b2m[2] = NAV_Data_Full_p->SubKF.Q_b2m[2] / normQ;
					NAV_Data_Full_p->SubKF.Q_b2m[3] = NAV_Data_Full_p->SubKF.Q_b2m[3] / normQ;
					//
					Qnb2Cnb(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Cmb);
					qnb2att(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.att_xyz); //?????????
					NAV_Data_Full_p->ODS.att_ods2_b_filte[0] = NAV_Data_Full_p->SubKF.att_xyz[0] * RAD2DEG;//*******
					NAV_Data_Full_p->ODS.att_ods2_b_filte[1] = NAV_Data_Full_p->SubKF.att_xyz[1] * RAD2DEG;;//***
					NAV_Data_Full_p->ODS.att_ods2_b_filte[2] = NAV_Data_Full_p->SubKF.att_xyz[2] * RAD2DEG;
					NAV_Data_Full_p->SubKF.Xk[0] = 0;
					NAV_Data_Full_p->SubKF.Xk[1] = 0;
					NAV_Data_Full_p->SubKF.Xk[2] = 0;
					//*******subkf********************************Guolong Zhang*******20240715*********************************<-**********
					//************1*****???????????**********
				}
				//**************2*********->*****
				//if (((NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED) || (NAV_Data_Full_p->GPS.NO_RTK_heading_flag))
				//	&& 1
				//	)
				{
					//						NAV_Data_Full_p->SINS.ods2gnss = CorrHeading(NAV_Data_Full_p->GPS.trackTrue + NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2] - NAV_Data_Full_p->GPS.Heading);
					//						if (fabs(NAV_Data_Full_p->SINS.ods2gnss) < 10)//?????????????headstatus??????????????????????10??********
					//							/*NAV_Data_Full_p->SINS.ods2gnss = 0;*/
					//						Scalar_KalmanFilte(&NAV_Data_Full_p->SINS.ods2gnssfilter, NAV_Data_Full_p->SINS.ods2gnss, &P_wheel_factor, Q_wheel_factor, R_wheel_factor / vb_sins4, 0.1);
											//R_gps_factor_rtk
					double  R_TMP = 0.0;
					R_TMP = R_gps_factor_rtk;
					if (NAV_Data_Full_p->GPS.NO_RTK_heading_flag)
					{
						R_TMP = R_gps_factor_spp;
					}
					Z_k = CorrHeading_PI(NAV_Data_Full_p->SINS.att[2] - NAV_Data_Full_p->GPS.Heading_cor * DEG2RAD);
					Scalar_KalmanFilte(&NAV_Data_Full_p->SubKF.att_b2gps[2], Z_k, &NAV_Data_Full_p->SubKF.P_b2gps, 1.0 * Q_gps_factor, R_TMP, 1.0 * DEG2RAD);//***rad****
				}
				//**************2**********<-****
			}
			else
			{
				NAV_Data_Full_p->Subkf2_Cnt = 0;  //******????????????????????****
			}
			//*************3***********->*********************
				{
				if(RETURN_SUCESS==NAV_Data_Full_p->ODS.ods_caculat_flag && fabs(NAV_Data_Full_p->ODS.WheelSpeed_ave)>0.5)
					{
//						if(fabs(v_wheel_ave)>2.0f)
//						{	
							//???????????????????????????????????????
							//NAV_Data_Full_p->ODS.scale_factor = vb_sins[1]/v_wheel_ave;	
						//	NAV_Data_Full_p->ODS.scale_factor =sqrt(vb_sins[0]*vb_sins[0]+vb_sins[1]*vb_sins[1]+vb_sins[2]*vb_sins[2])/v_wheel_ave;	
							//scale_factor_old = NAV_Data_Full_p->ODS.scale_factor;
							//Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.scale_factor_filte,NAV_Data_Full_p->ODS.scale_factor,&P_wheel_factor,Q_wheel_factor,R_wheel_factor/ vb_sins4,0.1);
						 double R_wheel_tmp=R_wheel_factor;
							if(NAV_Data_Full_p->TurnningFlag)
							{
								 R_wheel_tmp=4*R_wheel_factor;
							}
							R_wheel_tmp = R_wheel_tmp / fabs(NAV_Data_Full_p->ODS.WheelSpeed_ave)/ fabs(NAV_Data_Full_p->ODS.WheelSpeed_ave);
							NAV_Data_Full_p->ODS.scale_factor = mis_Zktmp[1]/NAV_Data_Full_p->ODS.WheelSpeed_ave;//********
					        Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.scale_factor_filte, NAV_Data_Full_p->ODS.scale_factor, &NAV_Data_Full_p->ODS.P_wheel_fact, 1.0*Q_wheel_factor, R_wheel_tmp,0.15);
							////if (NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED && NAV_Data_Full_p->KF.use_gps_flag == E_FUNSION_GPS)
							//{
							//		//if(E_SIM_MODE_NORMAL == combineData.Param.sim)
							//		{
							//			//if(testcount>=combineData.Param.lostepoch)
							//			//NAV_Data_Full_p->ODS.scale_factor_filte_RTK = NAV_Data_Full_p->ODS.scale_factor_filte;
							//		}
							//}
						//}
						//?????????????????????
						//NAV_Data_Full_p->SINS.ods2gnss = CorrHeading(NAV_Data_Full_p->SINS.ods2gnss-90- NAV_Data_Full_p->SINS.att[2]*RAD2DEG);
//						NAV_Data_Full_p->ODS.WheelSpeed_buffer[NAV_Data_Full_p->ODS.wheelHead] = NAV_Data_Full_p->ODS.WheelSpeed_ave;
//						NAV_Data_Full_p->ODS.wheelHead++;
//						NAV_Data_Full_p->ODS.wheelHead = NAV_Data_Full_p->ODS.wheelHead % WHEEL_BUFFER_SIZE;
						//inav_log(INAVMD(LOG_ERR),"scale_factor_filte=%f",NAV_Data_Full_p->ODS.scale_factor_filte);
					}
					else
					{
#ifdef WIN32
							//inav_log(INAVMD(LOG_ERR),"WheelSpeed=[%f,%f,%f,%f](km)",
							//NAV_Data_Full_p->ODS.WheelSpeed_Front_Left,
							//NAV_Data_Full_p->ODS.WheelSpeed_Front_Right,
							//NAV_Data_Full_p->ODS.WheelSpeed_Back_Left,
							//NAV_Data_Full_p->ODS.WheelSpeed_Back_Right);
#endif	
					}
				}
			//**************3***********<-******************															
		//}		
	//}
#if 0
	
//	float vb_sins[3] = {0,0,0};
	if(1)//???SINS???????????
	{	
#if 0	
		for(i = 0;i<3;i++)
		{
			temp = 0;
			for(k = 0;k<3;k++)
			{
				temp += (float)NAV_Data_Full_p->SINS.Cb2n[k+3*i]*(float)NAV_Data_Full_p->SINS.vn[k];			
			}
			vb_sins[i] = temp;//??????????????
		}
#endif
		//????????????????
		matmul("TN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.vn, 0.0, vb_sins);
		double vb_sins4 = vb_sins[1] * vb_sins[1] * vb_sins[1] * vb_sins[1];
		if(1)
		{
			static int acc_count = 0;
			if(fabs(NAV_Data_Full_p->IMU.acc_use[1] - NAV_Data_Full_p->IMU.acc_use_pre[1]) <0.4
				&& fabs(NAV_Data_Full_p->IMU.acc_use[2] - NAV_Data_Full_p->IMU.acc_use_pre[2]) < 1
				&& fabs(NAV_Data_Full_p->IMU.gyro_use[2] -NAV_Data_Full_p->IMU.gyro_use[2]) < 2 *DEG2RAD
				&& fabs(NAV_Data_Full_p->ODS.ODS_anglespeed) <0.2
				)
			{
				acc_count++;
			}
			else {acc_count = 0;}
			//??4????????????????????????????????????3????????
			v_wheel_ave = WheelSpeedOptimize((double)NAV_Data_Full_p->ODS.WheelSpeed_Front_Left * KMPH2MPS,
				(double)NAV_Data_Full_p->ODS.WheelSpeed_Front_Right * KMPH2MPS,
				(double)NAV_Data_Full_p->ODS.WheelSpeed_Back_Left * KMPH2MPS,
				(double)NAV_Data_Full_p->ODS.WheelSpeed_Back_Right * KMPH2MPS,
				1,
				&valid_wheelspeed);
			NAV_Data_Full_p->ODS.WheelSpeed_ave = v_wheel_ave;
			if(fabs(vb_sins[1])>5.0f//2.0f//??????
			   && fabs(NAV_Data_Full_p->SINS.wb_ib[2]*RAD2DEG)<MIN_TURN_GYRO_VALUE//1.0f//0.25f//1.0f
			   && NAV_Data_Full_p->ODS.ods_caculat_flag == 1
			  && acc_count>=20
			   //&& E_NAV_STATUS_IN_NAV== NAV_Data_Full_p->Nav_Status
			  )			
			{	
                temp1 = (-vb_sins[2]/sqrt(vb_sins[0]*vb_sins[0]+vb_sins[1]*vb_sins[1]));//tanx
            	//temp1 = (-vb_sins[2]/vb_sins[1]);//
				temp2 = (vb_sins[0]/vb_sins[1]); //tany
				
                if((fabsf((float)temp1)<1.0f)&&(fabsf((float)temp2)<1.0f))
				{		
					//??????????????30??
					//temphead = atanf(temp2)*RAD2DEG;
					temphead = atan(temp2)*RAD2DEG;

					//if(fabsf((float)temphead)<10.0f)
					{
						//NAV_Data_Full_p->ODS.att_ods2_b[0] = atanf(temp1)*RAD2DEG;
						NAV_Data_Full_p->ODS.att_ods2_b[0] = atan(temp1)*RAD2DEG;
						//NAV_Data_Full_p->ODS.att_ods2_b[0] = asinf(temp1)*RAD2DEG;
						NAV_Data_Full_p->ODS.att_ods2_b[1] = 0;
						//NAV_Data_Full_p->ODS.att_ods2_b[2] = atanf(temp2)*RAD2DEG;
						NAV_Data_Full_p->ODS.att_ods2_b[2] = atan(temp2) * RAD2DEG - NAV_Data_Full_p->SINS.wnb[2]/2 *RAD2DEG * TS;
						//NAV_Data_Full_p->ODS.att_ods2_b[2] = asinf(temp2)*RAD2DEG;
	//			    	void Scalar_KalmanFilte(float* x,float zk,float* P,float Q,float R)
						Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[0],NAV_Data_Full_p->ODS.att_ods2_b[0],&P_Cvb[0],Q_Cvb[0]*0.0005,R_Cvb[0]/vb_sins4,1.0*DEG2RAD);
						//Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[2],NAV_Data_Full_p->ODS.att_ods2_b[2],&P_Cvb[2],Q_Cvb[2],R_Cvb[2],1*DEG2RAD);
						//wheel????????
						//static int count_nav = 0;
						//if (NAV_Data_Full_p->Nav_Status == E_NAV_STATUS_IN_NAV
						//	&& count_nav > SAMPLE_FREQ * 10)
						//{
						//		Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[2], NAV_Data_Full_p->ODS.att_ods2_b[2], &P_Cvb[2], Q_Cvb[2] * 0, R_Cvb[2] / vb_sins4, 1.0 * DEG2RAD);
						//		//NAV_Data_Full_p->ODS.att_ods2_b_filte[2] = 1.15;
						//}
						//else if (combineData.memsType == 1)
						//{
						//	Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[2], NAV_Data_Full_p->ODS.att_ods2_b[2], &P_Cvb[2], Q_Cvb[2] * 0.05, R_Cvb[2] / vb_sins4, 1.0 * DEG2RAD);
						//}
						//else Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.att_ods2_b_filte[2],NAV_Data_Full_p->ODS.att_ods2_b[2],&P_Cvb[2],Q_Cvb[2]*0.005,R_Cvb[2]/ vb_sins4, 1.0 * DEG2RAD);
						//count_nav++;
						
						//NAV_Data_Full_p->ODS.att_ods2_b_filte[2] = 1.30;
					}
				}

					
					
					if (NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
						&& NAV_Data_Full.GPS.headingStatus == E_GPS_RTK_FIXED)
					{
						NAV_Data_Full_p->SINS.ods2gnss = CorrHeading(NAV_Data_Full_p->GPS.trackTrue + NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2] - NAV_Data_Full_p->GPS.Heading);
						if (fabs(NAV_Data_Full_p->SINS.ods2gnss) < 10)//?????????????headstatus??????????????????????10??
							/*NAV_Data_Full_p->SINS.ods2gnss = 0;*/
						Scalar_KalmanFilte(&NAV_Data_Full_p->SINS.ods2gnssfilter, NAV_Data_Full_p->SINS.ods2gnss, &P_wheel_factor, Q_wheel_factor, R_wheel_factor / vb_sins4, 0.1);
					}
					//if (NAV_Data_Full_p->ODS.ODS_anglespeed < 10*DEG2RAD)
					//{
					//	//v_wheel_ave = NAV_Data_Full_p->ODS.WheelSpeed_Back_Right * KMPH2MPS + NAV_Data_Full_p->ODS.WheelSpeed_Back_Left * KMPH2MPS;
					//	//v_wheel_ave = v_wheel_ave * 0.5;
					//}
					//v_wheel_ave = (NAV_Data_Full_p->ODS.WheelSpeed_Back_Left * KMPH2MPS+ NAV_Data_Full_p->ODS.WheelSpeed_Back_Right * KMPH2MPS)/2;//???????????
					
#if 0					
				    v_wheel[0] = NAV_Data_Full_p->ODS.WheelSpeed_Front_Left*KMPH2MPS;
				    v_wheel[1] = NAV_Data_Full_p->ODS.WheelSpeed_Front_Left*KMPH2MPS;
				    v_wheel[2] = NAV_Data_Full_p->ODS.WheelSpeed_Back_Left*KMPH2MPS;
				    v_wheel[3] = NAV_Data_Full_p->ODS.WheelSpeed_Back_Right*KMPH2MPS;
				
				    v_wheel_ave = 0.25f*(v_wheel[0]+v_wheel[1]+v_wheel[2]+v_wheel[3]);	
					//d_v_wheel??????????d_v_wheel_max[0]?????????????????d_v_wheel_max[1]?????????
					for(i = 0;i<4;i++)	
					{
						 d_v_wheel[i] = fabsf(v_wheel[i]-v_wheel_ave);
					}				
				    d_v_wheel_max[0] = 0;
				    d_v_wheel_max[1] = 0;
		            for(i = 0;i<4;i++)
				    {
						if (d_v_wheel[i] >= d_v_wheel_max[0])
						{
							d_v_wheel_max[0] = d_v_wheel[i];
							d_v_wheel_max[1] = i;
						}
					}
					//??????????????????????3????????
					v_wheel_ave = 0;
		            for(i = 0;i<4;i++)
				    {
						if(i != d_v_wheel_max[1])
						{
							v_wheel_ave += (0.3333333f*v_wheel[i]);
						}
					}
#endif				
					if(RETURN_SUCESS==valid_wheelspeed)
					{
						if(fabs(v_wheel_ave)>2.0f && acc_count >=20)
						{	
							//???????????????????????????????????????
							//NAV_Data_Full_p->ODS.scale_factor = vb_sins[1]/v_wheel_ave;	
							NAV_Data_Full_p->ODS.scale_factor =sqrt(vb_sins[0]*vb_sins[0]+vb_sins[1]*vb_sins[1]+vb_sins[2]*vb_sins[2])/v_wheel_ave;	
							scale_factor_old = NAV_Data_Full_p->ODS.scale_factor;
							Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.scale_factor_filte,NAV_Data_Full_p->ODS.scale_factor,&P_wheel_factor,Q_wheel_factor,R_wheel_factor/ vb_sins4,0.1);
							//if (NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED && NAV_Data_Full_p->KF.use_gps_flag == E_FUNSION_GPS)
							{
									//if(E_SIM_MODE_NORMAL == combineData.Param.sim)
									{
										//if(testcount>=combineData.Param.lostepoch)
										//NAV_Data_Full_p->ODS.scale_factor_filte_RTK = NAV_Data_Full_p->ODS.scale_factor_filte;
									}
							}
						}
						
						//?????????????????????
						//NAV_Data_Full_p->SINS.ods2gnss = atan2f((NAV_Data_Full_p->SINS.pos[0]- NAV_Data_Full_p->SINS.pos_pre[0]), (NAV_Data_Full_p->SINS.pos[1] - NAV_Data_Full_p->SINS.pos_pre[1]))*RAD2DEG;
						
						//NAV_Data_Full_p->SINS.ods2gnss = CorrHeading(NAV_Data_Full_p->SINS.ods2gnss-90- NAV_Data_Full_p->SINS.att[2]*RAD2DEG);
						
						NAV_Data_Full_p->ODS.WheelSpeed_buffer[NAV_Data_Full_p->ODS.wheelHead] = NAV_Data_Full_p->ODS.WheelSpeed_ave;
						NAV_Data_Full_p->ODS.wheelHead++;
						NAV_Data_Full_p->ODS.wheelHead = NAV_Data_Full_p->ODS.wheelHead % WHEEL_BUFFER_SIZE;
						
						
						//inav_log(INAVMD(LOG_ERR),"scale_factor_filte=%f",NAV_Data_Full_p->ODS.scale_factor_filte);
						
					}
					else
					{
#ifdef WIN32
							inav_log(INAVMD(LOG_ERR),"WheelSpeed=[%f,%f,%f,%f](km)",
							NAV_Data_Full_p->ODS.WheelSpeed_Front_Left,
							NAV_Data_Full_p->ODS.WheelSpeed_Front_Right,
							NAV_Data_Full_p->ODS.WheelSpeed_Back_Left,
							NAV_Data_Full_p->ODS.WheelSpeed_Back_Right);
#endif	
					}
				
			}			
		}
	}
	#endif
}

/******************************************************************************
*?  ??void DVL_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
*?  ????SINS?DVL?????????
*?  ??NAV_Data_Full_p
*?  ??DVL scale_factor and installation angles
*******************************************************************************/
void DVL_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i,j,k;
	double vb_sins[3] = {0};

	// DVL parameters similar to ODS but adapted for DVL
	#define  R_DVL_true  DVL_VEL_VAR
	#define  R_DVL_fake  VEL_VAR2
	#define  Qk_DVL   (0*1.0e-10)
	#define Q_DVL_gps_factor      (0*1.0e-12)
	#define R_DVL_gps_factor_rtk  (0.2*DEG2RAD * 0.2*DEG2RAD)
	#define R_DVL_gps_factor_spp  (0.4*DEG2RAD * 0.4*DEG2RAD)
	#define Q_DVL_factor   (1.0e-10)
	#define R_DVL_factor   (0.05*0.05)

	//DVL SubKF parameters
	double SubKF_Pk_[3*3]={0};
	double SubKF_Kg[3*3]={0};
	double misR[3]={0};
	double mis_Zk[3]={0};
	double mis_Zktmp[3]={0};
	double Mea_dvl[3]={0,0,0}; // DVL measurement in different directions
	double Kg_z[3]={0};

	double Hk[3*3]={0};
	double Hk_T[3*3]={0};
	double PHk_T[3*3]={0};

	double askew_vb[3*3]={0};
	double HPH_T_R[3*3]={0};
	double HPH_T_R_inv[3*3]={0};
	double Cbm[3*3]={0};
	double R_tmp=0.0;

	double KgH[3*3]={0};
	double KgHP[3*3]={0};

	double dq[4]={0};
	double normQ=0.0;
	double V_fake=0.0;
	double yaw_vel=0.0;
	double dvl_and_GPS=0.0;

	double Vim[3]={0};
	double Z_k=0.0;

	// Calculate body frame velocity from SINS
	matmul("TN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.vn, 0.0, vb_sins);
	// For DVL, we work directly in body frame, so no need for additional transformation
	// mis_Zktmp is the expected DVL measurement in body frame
	mis_Zktmp[0] = vb_sins[0];  // Forward velocity in body frame
	mis_Zktmp[1] = vb_sins[1];  // Right velocity in body frame
	mis_Zktmp[2] = vb_sins[2];  // Up velocity in body frame

	// DVL calibration processing
	if (sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn) > 0.5
		&& ((NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED) || (NAV_Data_Full_p->GPS.NO_RTK_heading_flag))
		&& (NAV_Data_Full_p->Nav_Status == E_NAV_STATUS_SYSTEM_STANDARD)
	   )
	{
		NAV_Data_Full_p->Subkf2_Cnt++;

		// DVL installation angle estimation using SubKF
		{
			V_fake = sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.vu * NAV_Data_Full_p->GPS.vu);
			yaw_vel = -atan2(NAV_Data_Full_p->GPS.ve, NAV_Data_Full_p->GPS.vn) * RAD2DEG;
			dvl_and_GPS = (yaw_vel - NAV_Data_Full_p->GPS.Heading_cor);
			if (!(fabs(dvl_and_GPS) < 30))
			{
				V_fake = -V_fake;
			}

			if (NAV_Data_Full_p->DVL.dvl_caculat_flag && NAV_Data_Full_p->DVL.dvl_normal_flag)
			{
				R_tmp = R_DVL_true;
				// DVL measures 3-axis velocity in body frame
				Mea_dvl[0] = NAV_Data_Full_p->DVL.DVL_SPEED_Front; // Forward velocity
				Mea_dvl[1] = NAV_Data_Full_p->DVL.DVL_SPEED_Right; // Right velocity
				Mea_dvl[2] = NAV_Data_Full_p->DVL.DVL_SPEED_Up;    // Up velocity
			}
			else
			{
				R_tmp = R_DVL_fake;
				// Use GPS velocity projected to body frame as fake measurement
				Mea_dvl[0] = V_fake;  // Assume forward motion
				Mea_dvl[1] = 0.0;     // No lateral motion
				Mea_dvl[2] = 0.0;     // No vertical motion
			}

			// Set measurement noise
			misR[0] = DVL_VEL_VAR;
			misR[1] = R_tmp;
			misR[2] = DVL_VEL_VAR;

			// Increase noise during turning
			if (NAV_Data_Full_p->TurnningFlag)
			{
				misR[0] = 4 * DVL_VEL_VAR;
				misR[1] = 4 * R_tmp;
				misR[2] = 4 * DVL_VEL_VAR;
			}

			// For DVL, H matrix is identity since we measure body frame velocity directly
			// H = I (3x3 identity matrix) for the velocity states
			Hk[0] = 1.0; Hk[1] = 0.0; Hk[2] = 0.0;
			Hk[3] = 0.0; Hk[4] = 1.0; Hk[5] = 0.0;
			Hk[6] = 0.0; Hk[7] = 0.0; Hk[8] = 1.0;
			Mat_Tr(3, Hk, Hk_T);

			mis_Zk[0] = mis_Zktmp[0] - Mea_dvl[0];
			mis_Zk[1] = mis_Zktmp[1] - Mea_dvl[1];
			mis_Zk[2] = mis_Zktmp[2] - Mea_dvl[2];

			// Time update
			for (i = 0; i < 3; i++)
			{
				for (j = 0; j < 3; j++)
				{
					if (i == j)
						SubKF_Pk_[i + 3 * j] = NAV_Data_Full_p->SubKF.Pk[i + 3 * j] + Qk_DVL;
					else
						SubKF_Pk_[i + 3 * j] = NAV_Data_Full_p->SubKF.Pk[i + 3 * j];
				}
			}

			matmul("NN", 3, 3, 3, 1.0, SubKF_Pk_, Hk_T, 0.0, PHk_T);
			matmul("NN", 3, 3, 3, 1.0, Hk, PHk_T, 0.0, HPH_T_R);

			// HPH_T+R
			HPH_T_R[0 + 3 * 0] = HPH_T_R[0 + 3 * 0] + misR[0];
			HPH_T_R[1 + 3 * 1] = HPH_T_R[1 + 3 * 1] + misR[1];
			HPH_T_R[2 + 3 * 2] = HPH_T_R[2 + 3 * 2] + misR[2];

			Mat3_Inv(HPH_T_R, HPH_T_R_inv);
			matmul("NN", 3, 3, 3, 1.0, PHk_T, HPH_T_R_inv, 0.0, SubKF_Kg);
			matmul("NN", 3, 1, 3, 1.0, SubKF_Kg, mis_Zk, 0.0, Kg_z);

			// Measurement update
			NAV_Data_Full_p->SubKF.Xk[0] = Kg_z[0];
			NAV_Data_Full_p->SubKF.Xk[1] = Kg_z[1];
			NAV_Data_Full_p->SubKF.Xk[2] = Kg_z[2];

			matmul("NN", 3, 3, 3, 1.0, SubKF_Kg, Hk, 0.0, KgH);
			matmul("NN", 3, 3, 3, -1.0, KgH, SubKF_Pk_, 0.0, KgHP);
			matrixSum(SubKF_Pk_, KgHP, 3, 3, 1.0, NAV_Data_Full_p->SubKF.Pk);

			// Symmetrize covariance matrix
			symmetry(NAV_Data_Full_p->SubKF.Pk, 3, NAV_Data_Full_p->SubKF.Pk);

			// Feedback
			qdelphi(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Xk, NAV_Data_Full_p->SubKF.Q_b2m);

			// Quaternion normalization
			normQ = sqrt(NAV_Data_Full_p->SubKF.Q_b2m[0] * NAV_Data_Full_p->SubKF.Q_b2m[0]
				+ NAV_Data_Full_p->SubKF.Q_b2m[1] * NAV_Data_Full_p->SubKF.Q_b2m[1]
				+ NAV_Data_Full_p->SubKF.Q_b2m[2] * NAV_Data_Full_p->SubKF.Q_b2m[2]
				+ NAV_Data_Full_p->SubKF.Q_b2m[3] * NAV_Data_Full_p->SubKF.Q_b2m[3]
			);
			NAV_Data_Full_p->SubKF.Q_b2m[0] = NAV_Data_Full_p->SubKF.Q_b2m[0] / normQ;
			NAV_Data_Full_p->SubKF.Q_b2m[1] = NAV_Data_Full_p->SubKF.Q_b2m[1] / normQ;
			NAV_Data_Full_p->SubKF.Q_b2m[2] = NAV_Data_Full_p->SubKF.Q_b2m[2] / normQ;
			NAV_Data_Full_p->SubKF.Q_b2m[3] = NAV_Data_Full_p->SubKF.Q_b2m[3] / normQ;

			Qnb2Cnb(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Cmb);
			qnb2att(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.att_xyz);
			NAV_Data_Full_p->DVL.att_dvl2_b_filte[0] = NAV_Data_Full_p->SubKF.att_xyz[0] * RAD2DEG;
			NAV_Data_Full_p->DVL.att_dvl2_b_filte[1] = NAV_Data_Full_p->SubKF.att_xyz[1] * RAD2DEG;
			NAV_Data_Full_p->DVL.att_dvl2_b_filte[2] = NAV_Data_Full_p->SubKF.att_xyz[2] * RAD2DEG;
			NAV_Data_Full_p->SubKF.Xk[0] = 0;
			NAV_Data_Full_p->SubKF.Xk[1] = 0;
			NAV_Data_Full_p->SubKF.Xk[2] = 0;
		}
	}
	else
	{
		NAV_Data_Full_p->Subkf2_Cnt = 0;
	}

	// DVL scale factor estimation
	{
		// Use forward velocity for scale factor estimation (primary motion direction)
		if(RETURN_SUCESS==NAV_Data_Full_p->DVL.dvl_caculat_flag && fabs(NAV_Data_Full_p->DVL.DVL_SPEED_Front)>0.5)
		{
			double R_dvl_tmp=R_DVL_factor;
			if(NAV_Data_Full_p->TurnningFlag)
			{
				 R_dvl_tmp=4*R_DVL_factor;
			}
			R_dvl_tmp = R_dvl_tmp / fabs(NAV_Data_Full_p->DVL.DVL_SPEED_Front)/ fabs(NAV_Data_Full_p->DVL.DVL_SPEED_Front);
			// Use forward velocity component for scale factor estimation
			NAV_Data_Full_p->DVL.scale_factor_dvl = mis_Zktmp[0]/NAV_Data_Full_p->DVL.DVL_SPEED_Front;
	        Scalar_KalmanFilte(&NAV_Data_Full_p->DVL.scale_factor_dvl_filte, NAV_Data_Full_p->DVL.scale_factor_dvl, &NAV_Data_Full_p->DVL.P_dvl_fact, 1.0*Q_DVL_factor, R_dvl_tmp,0.15);
		}
		else
		{
#ifdef WIN32
			//inav_log(INAVMD(LOG_ERR),"DVL Speed=[%f,%f,%f](m/s)",
			//NAV_Data_Full_p->DVL.DVL_SPEED_Front,
			//NAV_Data_Full_p->DVL.DVL_SPEED_Right,
			//NAV_Data_Full_p->DVL.DVL_SPEED_Up);
#endif
		}
	}
}