﻿/***********************************************************************************
船载升沉补偿算法实现
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2024-12-30        | 船载升沉补偿算法实现             |
|-----------+---------------+-------------------|
***********************************************************************************/

#include "nav_includes.h"
#include <string.h>
#include <math.h>

// 升沉滤波器状态结构
typedef struct {
    double x[2];        // 状态向量 [升沉位移, 升沉速度]
    double P[4];        // 协方差矩阵 2x2
    double Q[4];        // 过程噪声协方差矩阵 2x2
    double R;           // 观测噪声方差
    double dt;          // 采样时间间隔
    double fc;          // 截止频率
    int initialized;    // 初始化标志
} HeaveFilter_t;

// 全局升沉滤波器实例
static HeaveFilter_t g_heave_filter = { 0 };

// 升沉约束滤波器状态
typedef struct {
    double heave_displacement;      // 升沉位移 (m)
    double heave_velocity;         // 升沉速度 (m/s)
    double heave_acceleration;     // 升沉加速度 (m/s²)
    double filtered_attitude[3];   // 滤波后的姿态角 [roll, pitch, yaw] (rad)
    double attitude_constraint[3]; // 姿态约束量 (rad)
    double velocity_constraint[3]; // 速度约束量 (m/s)
    int valid;                     // 数据有效标志
} HeaveConstraint_t;

// 全局升沉约束状态
static HeaveConstraint_t g_heave_constraint = { 0 };

/**
 * @brief 初始化升沉滤波器
 * @param filter_freq 滤波器截止频率 (Hz)
 * @param dt 采样时间间隔 (s)
 */
void HeaveFilter_Init(double filter_freq, double dt)
{
    memset(&g_heave_filter, 0, sizeof(HeaveFilter_t));

    g_heave_filter.dt = dt;
    g_heave_filter.fc = filter_freq;

    // 初始化状态向量
    g_heave_filter.x[0] = 0.0;  // 升沉位移
    g_heave_filter.x[1] = 0.0;  // 升沉速度

    // 初始化协方差矩阵 P
    g_heave_filter.P[0] = 1.0;   // P[0,0] - 位移方差
    g_heave_filter.P[1] = 0.0;   // P[0,1] - 协方差
    g_heave_filter.P[2] = 0.0;   // P[1,0] - 协方差
    g_heave_filter.P[3] = 1.0;   // P[1,1] - 速度方差

    // 初始化过程噪声协方差矩阵 Q
    double q_pos = 0.01;  // 位移过程噪声
    double q_vel = 0.1;   // 速度过程噪声
    g_heave_filter.Q[0] = q_pos * dt * dt;
    g_heave_filter.Q[1] = 0.0;
    g_heave_filter.Q[2] = 0.0;
    g_heave_filter.Q[3] = q_vel * dt;

    // 观测噪声方差
    g_heave_filter.R = 0.1;

    g_heave_filter.initialized = 1;
}

/**
 * @brief 升沉Kalman滤波器预测步骤
 */
static void HeaveFilter_Predict(void)
{
    if (!g_heave_filter.initialized) return;

    double dt = g_heave_filter.dt;

    // 状态转移矩阵 F = [1, dt; 0, 1]
    double F[4] = { 1.0, dt, 0.0, 1.0 };

    // 状态预测: x_k|k-1 = F * x_k-1|k-1
    double x_pred[2];
    x_pred[0] = F[0] * g_heave_filter.x[0] + F[1] * g_heave_filter.x[1];
    x_pred[1] = F[2] * g_heave_filter.x[0] + F[3] * g_heave_filter.x[1];

    // 协方差预测: P_k|k-1 = F * P_k-1|k-1 * F' + Q
    double P_pred[4];
    // P_pred = F * P * F'
    P_pred[0] = F[0] * (F[0] * g_heave_filter.P[0] + F[2] * g_heave_filter.P[1]) +
        F[1] * (F[0] * g_heave_filter.P[2] + F[2] * g_heave_filter.P[3]);
    P_pred[1] = F[0] * (F[1] * g_heave_filter.P[0] + F[3] * g_heave_filter.P[1]) +
        F[1] * (F[1] * g_heave_filter.P[2] + F[3] * g_heave_filter.P[3]);
    P_pred[2] = F[2] * (F[0] * g_heave_filter.P[0] + F[2] * g_heave_filter.P[1]) +
        F[3] * (F[0] * g_heave_filter.P[2] + F[2] * g_heave_filter.P[3]);
    P_pred[3] = F[2] * (F[1] * g_heave_filter.P[0] + F[3] * g_heave_filter.P[1]) +
        F[3] * (F[1] * g_heave_filter.P[2] + F[3] * g_heave_filter.P[3]);

    // 加上过程噪声
    P_pred[0] += g_heave_filter.Q[0];
    P_pred[1] += g_heave_filter.Q[1];
    P_pred[2] += g_heave_filter.Q[2];
    P_pred[3] += g_heave_filter.Q[3];

    // 更新状态和协方差
    g_heave_filter.x[0] = x_pred[0];
    g_heave_filter.x[1] = x_pred[1];
    memcpy(g_heave_filter.P, P_pred, sizeof(P_pred));
}

/**
 * @brief 升沉Kalman滤波器更新步骤
 * @param z_heave 升沉观测值 (m)
 */
static void HeaveFilter_Update(double z_heave)
{
    if (!g_heave_filter.initialized) return;

    // 观测矩阵 H = [1, 0] (只观测位移)
    double H[2] = { 1.0, 0.0 };

    // 计算新息 y = z - H * x
    double y = z_heave - (H[0] * g_heave_filter.x[0] + H[1] * g_heave_filter.x[1]);

    // 计算新息协方差 S = H * P * H' + R
    double S = H[0] * (H[0] * g_heave_filter.P[0] + H[1] * g_heave_filter.P[2]) +
        H[1] * (H[0] * g_heave_filter.P[1] + H[1] * g_heave_filter.P[3]) +
        g_heave_filter.R;

    if (fabs(S) < 1e-10) return; // 避免除零

    // 计算Kalman增益 K = P * H' / S
    double K[2];
    K[0] = (g_heave_filter.P[0] * H[0] + g_heave_filter.P[1] * H[1]) / S;
    K[1] = (g_heave_filter.P[2] * H[0] + g_heave_filter.P[3] * H[1]) / S;

    // 状态更新 x = x + K * y
    g_heave_filter.x[0] += K[0] * y;
    g_heave_filter.x[1] += K[1] * y;

    // 协方差更新 P = (I - K * H) * P
    double I_KH[4];
    I_KH[0] = 1.0 - K[0] * H[0];
    I_KH[1] = -K[0] * H[1];
    I_KH[2] = -K[1] * H[0];
    I_KH[3] = 1.0 - K[1] * H[1];

    double P_new[4];
    P_new[0] = I_KH[0] * g_heave_filter.P[0] + I_KH[1] * g_heave_filter.P[2];
    P_new[1] = I_KH[0] * g_heave_filter.P[1] + I_KH[1] * g_heave_filter.P[3];
    P_new[2] = I_KH[2] * g_heave_filter.P[0] + I_KH[3] * g_heave_filter.P[2];
    P_new[3] = I_KH[2] * g_heave_filter.P[1] + I_KH[3] * g_heave_filter.P[3];

    memcpy(g_heave_filter.P, P_new, sizeof(P_new));
}

/**
 * @brief 从加速度计数据计算升沉位移
 * @param acc_z 垂直方向加速度 (m/s²)
 * @param att 当前姿态角 [roll, pitch, yaw] (rad)
 * @return 升沉位移 (m)
 */
static double CalculateHeaveFromAccel(double acc_z, double* att)
{
    // 将加速度从载体坐标系转换到导航坐标系
    double cos_roll = cos(att[0]);
    double sin_roll = sin(att[0]);
    double cos_pitch = cos(att[1]);
    double sin_pitch = sin(att[1]);

    // 计算垂直方向的重力补偿加速度
    double acc_vertical = acc_z * cos_roll * cos_pitch - G0;

    // 通过二次积分得到升沉位移（简化处理）
    static double heave_vel = 0.0;
    static double heave_pos = 0.0;

    double dt = g_heave_filter.dt;
    heave_vel += acc_vertical * dt;
    heave_pos += heave_vel * dt;

    // 应用高通滤波去除低频漂移
    double alpha = 2.0 * PI * g_heave_filter.fc * dt;
    double filter_coeff = alpha / (1.0 + alpha);

    static double heave_pos_filtered = 0.0;
    heave_pos_filtered = filter_coeff * heave_pos + (1.0 - filter_coeff) * heave_pos_filtered;

    return heave_pos - heave_pos_filtered;
}

/**
 * @brief 升沉补偿主处理函数
 * @param NAV_Data_Full_p 导航数据指针
 */
void HeaveCompensation_Process(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    if (!NAV_Data_Full_p) return;

    // 检查是否启用船载模式
    extern CombineDataTypeDef combineData;
    if (combineData.Param.marine_mode == 0) {
        return; // 非船载模式，不进行升沉补偿
    }

    // 初始化升沉滤波器（如果未初始化）
    if (!g_heave_filter.initialized) {
        HeaveFilter_Init(combineData.Param.heave_filter_freq, TS);
    }

    // 获取当前姿态角
    double current_att[3];
    current_att[0] = NAV_Data_Full_p->SINS.att[0]; // roll
    current_att[1] = NAV_Data_Full_p->SINS.att[1]; // pitch  
    current_att[2] = NAV_Data_Full_p->SINS.att[2]; // yaw

    // 从加速度计计算升沉位移
    double heave_obs = CalculateHeaveFromAccel(NAV_Data_Full_p->IMU.acc_use[2], current_att);

    // 升沉滤波器预测和更新
    HeaveFilter_Predict();
    HeaveFilter_Update(heave_obs);

    // 更新升沉约束状态
    g_heave_constraint.heave_displacement = g_heave_filter.x[0];
    g_heave_constraint.heave_velocity = g_heave_filter.x[1];
    g_heave_constraint.heave_acceleration = (g_heave_filter.x[1] - g_heave_constraint.heave_velocity) / TS;

    // 计算姿态约束
    double constraint_factor = combineData.Param.heave_constraint_factor;
    g_heave_constraint.attitude_constraint[0] = current_att[0] * constraint_factor;
    g_heave_constraint.attitude_constraint[1] = current_att[1] * constraint_factor;
    g_heave_constraint.attitude_constraint[2] = 0.0; // 航向角不受升沉影响

    // 计算速度约束
    double vel_constraint_factor = combineData.Param.marine_velocity_constraint;
    g_heave_constraint.velocity_constraint[0] = NAV_Data_Full_p->SINS.vn[0] * vel_constraint_factor;
    g_heave_constraint.velocity_constraint[1] = NAV_Data_Full_p->SINS.vn[1] * vel_constraint_factor;
    g_heave_constraint.velocity_constraint[2] = g_heave_filter.x[1]; // 垂直速度使用升沉速度

    // 应用姿态约束滤波
    double att_constraint_factor = combineData.Param.marine_attitude_constraint;
    g_heave_constraint.filtered_attitude[0] = current_att[0] * (1.0 - att_constraint_factor) +
        g_heave_constraint.attitude_constraint[0] * att_constraint_factor;
    g_heave_constraint.filtered_attitude[1] = current_att[1] * (1.0 - att_constraint_factor) +
        g_heave_constraint.attitude_constraint[1] * att_constraint_factor;
    g_heave_constraint.filtered_attitude[2] = current_att[2]; // 航向角保持不变

    g_heave_constraint.valid = 1;
}

/**
 * @brief 获取升沉补偿结果
 * @param heave_displacement 升沉位移输出 (m)
 * @param heave_velocity 升沉速度输出 (m/s)
 * @param filtered_attitude 滤波后姿态角输出 [roll, pitch, yaw] (rad)
 * @return 0-成功, -1-失败
 */
int HeaveCompensation_GetResult(double* heave_displacement, double* heave_velocity, double* filtered_attitude)
{
    if (!g_heave_constraint.valid) {
        return -1;
    }

    if (heave_displacement) {
        *heave_displacement = g_heave_constraint.heave_displacement;
    }

    if (heave_velocity) {
        *heave_velocity = g_heave_constraint.heave_velocity;
    }

    if (filtered_attitude) {
        filtered_attitude[0] = g_heave_constraint.filtered_attitude[0];
        filtered_attitude[1] = g_heave_constraint.filtered_attitude[1];
        filtered_attitude[2] = g_heave_constraint.filtered_attitude[2];
    }

    return 0;
}

/**
 * @brief 重置升沉补偿算法
 */
void HeaveCompensation_Reset(void)
{
    memset(&g_heave_filter, 0, sizeof(HeaveFilter_t));
    memset(&g_heave_constraint, 0, sizeof(HeaveConstraint_t));
}
