﻿  gnss.c
  main.c
  nav.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(134,13): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(814,12): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1389,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1470,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1486,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1542,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1562,18): warning C4477: “sprintf”: 格式字符串“%d”需要类型“int”的参数，但可变参数 15 拥有了类型“double”
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1582,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1651,40): warning C4305: “=”: 从“double”到“float”截断
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1825,17): warning C4101: “buffer”: 未引用的局部变量
  nav_app.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(358,2): warning C4013: “PrintOutStateChange”未定义；假设外部返回 int
  nav_cli.c
  nav_gnss.c
  nav_heave.c
  nav_imu.c
  nav_kf.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(256,6): warning C4101: “i”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(3393,9): warning C4101: “posL_”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(3392,9): warning C4101: “vnL_”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(3390,22): warning C4101: “MpvVn”: 未引用的局部变量
  nav_magnet.c
  nav_mahony.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_mahony.c(253,9): warning C4101: “Cnb”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_mahony.c(252,17): warning C4101: “fb”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_mahony.c(252,9): warning C4101: “wib”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_mahony.c(252,34): warning C4101: “win_b”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_mahony.c(252,24): warning C4101: “wie_b”: 未引用的局部变量
  nav_math.c
  nav_ods.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_ods.c(407,10): warning C4101: “k”: 未引用的局部变量
  nav_sins.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_sins.c(1598,8): warning C4101: “j”: 未引用的局部变量
  navlog.c
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(177,15): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(183,15): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(189,18): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(194,15): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
  正在生成代码
  3 of 90 functions ( 3.3%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    3 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  tlh_ins632332_20231121.vcxproj -> E:\INS_Code\组合算法后处理VS_underwater\组合算法后处理VS\POST_Raw_Version_LiGong\x64\Release\tlh_ins632332_20231121.exe
